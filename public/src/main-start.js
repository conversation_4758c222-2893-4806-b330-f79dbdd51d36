/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./src/ability/FirstPersonControlsAbility.js":
/*!***************************************************!*\
  !*** ./src/ability/FirstPersonControlsAbility.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FirstPersonControlsAbility: () => (/* binding */ FirstPersonControlsAbility)\n/* harmony export */ });\n/**\r\n * 第一人称相机控制能力\r\n */\r\nclass FirstPersonControlsAbility {\r\n    /**\r\n     * @param {THREE.PerspectiveCamera} ctx.camera The camera to control.\r\n     */\r\n    constructor(ctx, opts = {}) {\r\n        this.ctx = ctx;\r\n\r\n        const { THREE } = this.ctx.imports;\r\n        // 用户能否控制\r\n        this.canUserControl = true;\r\n        this.isUserControling = false;\r\n        this.userLastControlTime = 0;\r\n        this.mousedownTime = 0;\r\n        this.mouseupTime = 0;\r\n\r\n        // 存储之前的相机数据，用于检测变化\r\n        this._preData = {\r\n            position: new THREE.Vector3(),\r\n            rotation: new THREE.Euler(),\r\n            quaternion: new THREE.Quaternion()\r\n        };\r\n        this._hasFirstRestoreByData = false;\r\n\r\n        this.PLAYER_HEIGHT = 1.8;\r\n        // 走路速度\r\n        this.moveSpeed = 15;\r\n        // 跑步速度\r\n        this.fastMoveSpeed = 30;\r\n        // 重力加速度\r\n        this.gravity = 30;\r\n        // 跳跃强度\r\n        this.jumpStrength = 12;\r\n        // 垂直速度\r\n        this.verticalVelocity = 0;\r\n        // 能否跳跃\r\n        this.canJump = true;\r\n        this.raycaster = new THREE.Raycaster();\r\n        this.keysPressed = {};\r\n\r\n        // 是否启用碰撞检测\r\n        this.collisionEnabled = opts.collisionEnabled ?? true;\r\n        // 是否启用上升下降\r\n        this.upDownEnabled = opts.upDownEnabled ?? false;\r\n\r\n        this.useRightMouseDown = opts.useRightMouseDown ?? false;\r\n\r\n\r\n        if (opts.playerHeight >= 0) this.PLAYER_HEIGHT = opts.playerHeight;\r\n        else if (ctx.data.player.heightUnit >= 0) this.PLAYER_HEIGHT = ctx.utils.getRealSize(ctx.data.player.heightUnit);\r\n\r\n        if (opts.moveSpeed >= 0) this.moveSpeed = opts.moveSpeed;\r\n        else if (ctx.data.player.moveSpeedUnit >= 0) this.moveSpeed = ctx.utils.getRealSize(ctx.data.player.moveSpeedUnit);\r\n\r\n        if (opts.fastMoveSpeed >= 0) this.fastMoveSpeed = opts.fastMoveSpeed;\r\n        else if (ctx.data.player.fastMoveSpeedUnit >= 0) this.fastMoveSpeed = ctx.utils.getRealSize(ctx.data.player.fastMoveSpeedUnit);\r\n\r\n        if (opts.jumpStrength >= 0) this.jumpStrength = opts.jumpStrength;\r\n        else if (ctx.data.player.jumpStrengthUnit >= 0) this.jumpStrength = ctx.utils.getRealSize(ctx.data.player.jumpStrengthUnit);\r\n\r\n        if (opts.gravity >= 0) this.gravity = opts.gravity;\r\n        else if (ctx.data.world.gravityUnit >= 0) this.gravity = ctx.utils.getRealSize(ctx.data.world.gravityUnit);\r\n    }\r\n\r\n    /**\r\n     * 初始化\r\n     * @param {*} pars \r\n     */\r\n    init(pars) {\r\n        const self = this;\r\n        // Event Listeners for Keyboard\r\n        document.addEventListener('keydown', (event) => {\r\n            self.keysPressed[event.key.toLowerCase()] = true;\r\n            // 记录用户控制\r\n            self.isUserControling = true;\r\n            self.userLastControlTime = Date.now();\r\n        }, false);\r\n        document.addEventListener('keyup', (event) => {\r\n            self.keysPressed[event.key.toLowerCase()] = false;\r\n            // 记录用户控制\r\n            self.isUserControling = true;\r\n            self.userLastControlTime = Date.now();\r\n        }, false);\r\n    }\r\n\r\n    /**\r\n     * 启动\r\n     */\r\n    start(pars) {\r\n        const self = this;\r\n        const { cgWorld } = pars;\r\n\r\n        /**\r\n         * 拦截鼠标或触摸事件\r\n         * @param {MouseEvent|TouchEvent} e\r\n         */\r\n        const userControlsAbility = cgWorld.getAbility('user_ctrl');\r\n\r\n        let mousedownName = 'mouseleftdown';\r\n        if (self.useRightMouseDown) {\r\n            mousedownName = 'mouserightdown';\r\n        }\r\n        console.log(mousedownName)\r\n\r\n        userControlsAbility.setUserEvent('first_person_controls',\r\n            [mousedownName, \"mouseup\", \"mousemove\", \"touchstart\", \"touchend\", \"touchmove\"], (e, type) => {\r\n            console.log(type)\r\n\r\n                if (type.endsWith('down') || type.endsWith('start')) {\r\n                    // 记录用户控制\r\n                    self.isUserControling = true;\r\n                    self.userLastControlTime = Date.now();\r\n                    self.mousedownTime = Date.now();\r\n                }\r\n\r\n                if (type.endsWith('move')) {\r\n                    if (self.isUserControling && self.mousedownTime > self.mouseupTime) {\r\n                        self.userLastControlTime = Date.now();\r\n                    }\r\n                }\r\n\r\n                if (type.endsWith('up') || type.endsWith('end')) {\r\n                    self.mouseupTime = Date.now();\r\n                    self.isUserControling = false;\r\n                    self._touchUserChangeCameraDelay();\r\n                }\r\n            });\r\n    }\r\n\r\n    /**\r\n     * 动画更新触发\r\n     * Updates the camera position and state based on user input and physics.\r\n     */\r\n    update(pars) {\r\n        const self = this;\r\n        const { THREE } = this.ctx.imports;\r\n\r\n        // 渲染两帧画面之间所经过的时间\r\n        let delta = this.ctx.deltaTime;\r\n        // 获取相机 \r\n        let camera = this.ctx.camera;\r\n        // 获取玩家高度\r\n        let PLAYER_HEIGHT = this.PLAYER_HEIGHT;\r\n        // 获取轨道控制器\r\n        let controls = this._getOrbitControls(camera, this.ctx.renderer, PLAYER_HEIGHT);\r\n\r\n        if (controls.enabled !== this.canUserControl) {\r\n            if (this.canUserControl === false) {\r\n                controls.update();\r\n            }\r\n        }\r\n        // 控制OrbitControls的启用状态\r\n        controls.enabled = this.canUserControl;\r\n\r\n        // 当前按压的键字典\r\n        let keysPressed = this.keysPressed;\r\n        // 可碰撞对象数组\r\n        let collidableObjects;\r\n        if (this.collisionEnabled) {\r\n            collidableObjects = this.ctx.cgWorld.getCollidableObjects();\r\n        }\r\n        else {\r\n            collidableObjects = [];\r\n        }\r\n\r\n        // Movement with collision detection\r\n        // 检测是否按住shift键进行快速移动\r\n        const currentSpeed = keysPressed['shift'] ? this.fastMoveSpeed : this.moveSpeed;\r\n        const moveDistance = currentSpeed * delta;\r\n        const forward = new THREE.Vector3();\r\n        camera.getWorldDirection(forward);\r\n        forward.y = 0;\r\n        forward.normalize();\r\n\r\n        const right = new THREE.Vector3();\r\n        right.copy(forward).cross(camera.up).normalize();\r\n\r\n        // Store original position for collision rollback\r\n        const originalPosition = camera.position.clone();\r\n\r\n        if (keysPressed['w']) {\r\n            if (!this._checkWallCollision(camera.position, forward, moveDistance, collidableObjects)) {\r\n                camera.position.addScaledVector(forward, moveDistance);\r\n            }\r\n        }\r\n        if (keysPressed['s']) {\r\n            if (!this._checkWallCollision(camera.position, forward, -moveDistance, collidableObjects)) {\r\n                camera.position.addScaledVector(forward, -moveDistance);\r\n            }\r\n        }\r\n        if (keysPressed['a']) {\r\n            if (!this._checkWallCollision(camera.position, right, -moveDistance, collidableObjects)) {\r\n                camera.position.addScaledVector(right, -moveDistance);\r\n            }\r\n        }\r\n        if (keysPressed['d']) {\r\n            if (!this._checkWallCollision(camera.position, right, moveDistance, collidableObjects)) {\r\n                camera.position.addScaledVector(right, moveDistance);\r\n            }\r\n        }\r\n\r\n        // 上升下降\r\n        if (this.upDownEnabled) {\r\n            // 垂直移动 (Q键上升, E键下降)\r\n            if (keysPressed['q']) {\r\n                camera.position.y += moveDistance;\r\n            }\r\n            if (keysPressed['e']) {\r\n                camera.position.y -= moveDistance;\r\n            }\r\n        }\r\n\r\n        // Gravity and Jump\r\n        this._handleGroundCollision(camera, collidableObjects, delta, keysPressed);\r\n\r\n        // 防止相机穿墙 - 确保相机与墙面保持最小距离\r\n        this._preventCameraWallPenetration(camera, collidableObjects);\r\n\r\n        // Wall collision (simple)\r\n        // camera.position.x = Math.max(-this.roomSize.width/2 + 1, Math.min(this.roomSize.width/2 - 1, camera.position.x));\r\n        // camera.position.z = Math.max(-this.roomSize.depth/2 + 1, Math.min(this.roomSize.depth/2 - 1, camera.position.z));\r\n\r\n        // Update controls target\r\n        const lookDirection = new THREE.Vector3();\r\n        camera.getWorldDirection(lookDirection);\r\n        controls.target.copy(camera.position).add(lookDirection);\r\n\r\n        // Update controls after all position changes\r\n        controls.update();\r\n\r\n        if (this.isUserControling) {\r\n            const gucaeAbility = this.ctx.cgWorld.getAbility('get_user_change_any_event');\r\n            gucaeAbility.touchEvent('user-change-camera', {\r\n                canUserControl: self.canUserControl\r\n            });\r\n\r\n            if (Date.now() - this.userLastControlTime > 300) {\r\n                self._touchUserChangeCameraDelay();\r\n            }\r\n        }\r\n\r\n        // 恢复相机状态\r\n        if (!this._hasFirstRestoreByData) {\r\n            this._hasFirstRestoreByData = true;\r\n\r\n            const ctx = this.ctx;\r\n            const data = ctx.data.camera;\r\n\r\n            if (data.position) ctx.camera.position.fromArray(data.position);\r\n            if (data.rotation) ctx.camera.rotation.fromArray(data.rotation);\r\n            if (data.up) ctx.camera.up.fromArray(data.up);\r\n            if (data.zoom) ctx.camera.zoom = data.zoom;\r\n            if (data.fov) ctx.camera.fov = data.fov;\r\n            if (data.near) ctx.camera.near = data.near;\r\n            if (data.far) ctx.camera.far = data.far;\r\n            if (data.aspect) ctx.camera.aspect = data.aspect;\r\n\r\n            // 设置玩家初始位置和朝向\r\n            if (this.ctx.enableDevViewport) {\r\n                console.log(`恢复相机状态...`)\r\n            }\r\n            else {\r\n                console.log(`设置玩家初始位置和朝向...`)\r\n                // 位置\r\n                const pos = this.ctx.utils.getRealSizeList(this.ctx.data.player.startPosUnit);\r\n                if (pos[1] <= 0) pos[1] = 1; // 防止掉落地面以下\r\n                self.ctx.camera.position.set(pos[0], pos[1], pos[2]);\r\n                console.log('位置坐标：', this.ctx.data.player.startPosUnit, pos)\r\n\r\n                // 朝向\r\n                let finalLookAtPosUnit = [\r\n                    this.ctx.data.player.startPosUnit[0] + this.ctx.data.player.startLookAtPosUnit[0], \r\n                    this.ctx.data.player.startPosUnit[1] + this.ctx.data.player.startLookAtPosUnit[1], \r\n                    this.ctx.data.player.startPosUnit[2] + this.ctx.data.player.startLookAtPosUnit[2]];\r\n                const lookAtPos = this.ctx.utils.getRealSizeList(finalLookAtPosUnit);\r\n                self.ctx.camera.lookAt(lookAtPos[0], lookAtPos[1], lookAtPos[2]);\r\n                console.log('朝向坐标：', finalLookAtPosUnit)\r\n            }\r\n        }\r\n    }\r\n\r\n    active() {\r\n        this.canUserControl = true;\r\n    }\r\n\r\n    deactive() {\r\n        this.canUserControl = false;\r\n    }\r\n\r\n    /**\r\n     * 获取镜头的当前数据，用于下次加载时恢复（坐标，角度，...）\r\n     */\r\n    getData() {\r\n        const camera = this.ctx.camera;\r\n        const controls = this.orbitControls;\r\n\r\n        // 如果有OrbitControls，需要先更新camera的matrix来获取正确的旋转信息\r\n        if (controls) {\r\n            controls.update();\r\n            camera.updateMatrixWorld();\r\n        }\r\n\r\n        return {\r\n            position: this.ctx.camera.position.toArray(),\r\n            rotation: this.ctx.camera.rotation.toArray(),\r\n            up: this.ctx.camera.up.toArray(),\r\n            target: controls.target.toArray(),\r\n            zoom: this.ctx.camera.zoom,\r\n            fov: this.ctx.camera.fov,\r\n            near: this.ctx.camera.near,\r\n            far: this.ctx.camera.far,\r\n            aspect: this.ctx.camera.aspect,\r\n        }\r\n    }\r\n\r\n    _touchUserChangeCameraDelay() {\r\n        const self = this;\r\n        clearTimeout(this.touchUserChangeCameraDelayThread)\r\n        this.touchUserChangeCameraDelayThread = setTimeout(() => {\r\n            // 同步相机数据\r\n            const data = self.getData();\r\n            for (const i in data) {\r\n                self.ctx.data.camera[i] = data[i];\r\n            }\r\n\r\n            if (self.canUserControl) {\r\n                const gucaeAbility = this.ctx.cgWorld.getAbility('get_user_change_any_event');\r\n                gucaeAbility.touchEvent('user-change-camera-delay', {});\r\n            }\r\n        }, 300)\r\n    }\r\n\r\n    /**\r\n     * 获取相机的轨道控制器\r\n     * @param {*} camera \r\n     * @param {*} renderer \r\n     * @param {*} PLAYER_HEIGHT \r\n     * @returns \r\n     */\r\n    _getOrbitControls(camera, renderer, PLAYER_HEIGHT) {\r\n        if (this.orbitControls == null) {\r\n            const { OrbitControls } = this.ctx.imports;\r\n\r\n            const orbitControls = new OrbitControls(camera, renderer.domElement);\r\n            orbitControls.enablePan = false;      // 禁用平移功能\r\n            orbitControls.enableZoom = false;     // 禁用缩放功能\r\n            orbitControls.enableDamping = true;   // 启用阻尼效果，使控制更平滑\r\n            orbitControls.screenSpacePanning = false; // 禁用屏幕空间平移\r\n            orbitControls.minDistance = 1;        // 设置相机到目标的最小距离\r\n            // orbitControls.maxDistance = 50;    // 设置相机到目标的最大距离\r\n            orbitControls.dampingFactor = 0.10;   // 设置阻尼系数，值越大阻尼效果越强（镜头惯性就会越小）\r\n            orbitControls.enableRotate = true;    // 启用旋转功能\r\n            orbitControls.rotateSpeed = 0.5;      // 设置旋转速度\r\n            orbitControls.minPolarAngle = 0;      // 设置垂直旋转的最小角度（弧度）\r\n            orbitControls.maxPolarAngle = Math.PI; // 设置垂直旋转的最大角度（弧度，Math.PI为180度）\r\n\r\n            orbitControls.target.set(0, PLAYER_HEIGHT, 0); // Set initial look-at target\r\n\r\n            this.orbitControls = orbitControls;\r\n        }\r\n\r\n        return this.orbitControls;\r\n    }\r\n\r\n    /**\r\n     * 检查水平移动时的墙壁碰撞\r\n     * @param {THREE.Vector3} currentPosition 当前位置\r\n     * @param {THREE.Vector3} direction 移动方向\r\n     * @param {number} distance 移动距离\r\n     * @param {Array} collidableObjects 可碰撞对象数组\r\n     * @returns {boolean} 是否发生碰撞\r\n     */\r\n    _checkWallCollision(currentPosition, direction, distance, collidableObjects) {\r\n        const { THREE } = this.ctx.imports;\r\n\r\n        // 玩家碰撞检测参数\r\n        const playerRadius = 0.5; // 玩家碰撞半径\r\n        const checkDistance = Math.abs(distance) + playerRadius; // 检测距离\r\n\r\n        // 从玩家胸部高度开始检测（不是脚部）\r\n        const rayOrigin = currentPosition.clone();\r\n        rayOrigin.y -= this.PLAYER_HEIGHT * 0.3; // 从胸部高度检测\r\n\r\n        // 标准化移动方向\r\n        const moveDirection = direction.clone().normalize();\r\n        if (distance < 0) {\r\n            moveDirection.multiplyScalar(-1);\r\n        }\r\n\r\n        // 多点检测，确保玩家身体不会穿过物体\r\n        const checkPoints = [\r\n            rayOrigin.clone(), // 中心点\r\n            rayOrigin.clone().add(new THREE.Vector3(playerRadius * 0.7, 0, 0)), // 右侧\r\n            rayOrigin.clone().add(new THREE.Vector3(-playerRadius * 0.7, 0, 0)), // 左侧\r\n            rayOrigin.clone().add(new THREE.Vector3(0, 0, playerRadius * 0.7)), // 前侧\r\n            rayOrigin.clone().add(new THREE.Vector3(0, 0, -playerRadius * 0.7)), // 后侧\r\n        ];\r\n\r\n        for (let point of checkPoints) {\r\n            this.raycaster.set(point, moveDirection);\r\n            const intersections = this.raycaster.intersectObjects(collidableObjects, true);\r\n\r\n            // 如果检测距离内有碰撞，则阻止移动\r\n            if (intersections.length > 0 && intersections[0].distance < checkDistance) {\r\n                return true; // 发生碰撞\r\n            }\r\n        }\r\n\r\n        return false; // 无碰撞\r\n    }\r\n\r\n    /**\r\n     * 防止相机穿墙 - 确保相机与墙面保持最小距离\r\n     * @param {THREE.PerspectiveCamera} camera 相机对象\r\n     * @param {Array} collidableObjects 可碰撞对象数组\r\n     */\r\n    _preventCameraWallPenetration(camera, collidableObjects) {\r\n        const { THREE } = this.ctx.imports;\r\n\r\n        const minDistance = 1; // 相机与墙面的最小距离\r\n        const checkDirections = [\r\n            new THREE.Vector3(1, 0, 0),   // 右\r\n            new THREE.Vector3(-1, 0, 0),  // 左\r\n            new THREE.Vector3(0, 0, 1),   // 前\r\n            new THREE.Vector3(0, 0, -1),  // 后\r\n            new THREE.Vector3(1, 0, 1).normalize(),   // 右前\r\n            new THREE.Vector3(-1, 0, 1).normalize(),  // 左前\r\n            new THREE.Vector3(1, 0, -1).normalize(),  // 右后\r\n            new THREE.Vector3(-1, 0, -1).normalize()  // 左后\r\n        ];\r\n\r\n        const rayOrigin = camera.position.clone();\r\n\r\n        for (let direction of checkDirections) {\r\n            this.raycaster.set(rayOrigin, direction);\r\n            const intersections = this.raycaster.intersectObjects(collidableObjects, true);\r\n\r\n            if (intersections.length > 0) {\r\n                const distance = intersections[0].distance;\r\n\r\n                // 如果距离小于最小距离，则将相机推离墙面\r\n                if (distance < minDistance) {\r\n                    const pushBack = minDistance - distance;\r\n                    const pushDirection = direction.clone().multiplyScalar(-pushBack);\r\n                    camera.position.add(pushDirection);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 处理地面碰撞检测和重力\r\n     * @param {THREE.PerspectiveCamera} camera 相机对象\r\n     * @param {Array} collidableObjects 可碰撞对象数组\r\n     * @param {number} delta 时间间隔\r\n     * @param {object} keysPressed 按键状态\r\n     */\r\n    _handleGroundCollision(camera, collidableObjects, delta, keysPressed) {\r\n        const { THREE } = this.ctx.imports;\r\n\r\n        // 检测地面碰撞\r\n        this.raycaster.set(camera.position, new THREE.Vector3(0, -1, 0));\r\n        const intersections = this.raycaster.intersectObjects(collidableObjects, false);\r\n        const onGround = intersections.length > 0 && intersections[0].distance < this.PLAYER_HEIGHT + 0.1;\r\n\r\n        if (onGround) {\r\n            const groundY = intersections[0].point.y;\r\n            if (this.verticalVelocity <= 0) {\r\n                this.verticalVelocity = 0;\r\n                camera.position.y = groundY + this.PLAYER_HEIGHT;\r\n                this.canJump = true;\r\n            }\r\n        } else {\r\n            this.verticalVelocity -= this.gravity * delta;\r\n            this.canJump = false;\r\n        }\r\n\r\n        // 处理跳跃\r\n        if (keysPressed[' '] && this.canJump) {\r\n            this.verticalVelocity = this.jumpStrength;\r\n            this.canJump = false;\r\n        }\r\n\r\n        // 应用垂直速度\r\n        camera.position.y += this.verticalVelocity * delta;\r\n    }\r\n}\r\n\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/ability/FirstPersonControlsAbility.js?\n}");

/***/ }),

/***/ "./src/ability/GetUserChangeAnyEventAbility.js":
/*!*****************************************************!*\
  !*** ./src/ability/GetUserChangeAnyEventAbility.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GetUserChangeAnyEventAbility: () => (/* binding */ GetUserChangeAnyEventAbility)\n/* harmony export */ });\n/* harmony import */ var _util_tjs_camera_util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/tjs-camera-util */ \"./src/util/tjs-camera-util.js\");\n/* harmony import */ var _util_tjs_geometry_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/tjs-geometry-util */ \"./src/util/tjs-geometry-util.js\");\n\r\n\r\n\r\n/**\r\n * 获取用户变动任何事物的事件（用户变动中心）\r\n */\r\nclass GetUserChangeAnyEventAbility {\r\n\r\n    constructor(ctx, data) {\r\n        this.ctx = ctx;\r\n        this.data = data;\r\n\r\n        this._eventListenerMap = {};\r\n    }\r\n\r\n    /**\r\n     * 初始化\r\n     * @param {*} pars \r\n     */\r\n    init(pars) {\r\n    }\r\n\r\n    /**\r\n     * 启动\r\n     */\r\n    start() {\r\n    }\r\n\r\n    /**\r\n     * 触发事件\r\n     * @param {*} name user-change-camera-delay 用户改变相机事件（延迟上报）\r\n     */\r\n    touchEvent(name, opts = {}) {\r\n        const self = this;\r\n        // console.log(`触发事件：${name}`, opts)\r\n\r\n        // 用户改变相机事件（延迟上报）\r\n        if (name === 'user-change-camera-delay') {\r\n            self._requestSaveProject();\r\n        }\r\n        else if (name === 'user-change-camera') {\r\n            self._showCameraToXZPlane();\r\n        }\r\n        else if (name === 'user-select-object') {\r\n\r\n        }\r\n        else if (name === 'user-move-object') {\r\n            if (self.ctx.propBarViewModel) {\r\n                self.ctx.propBarViewModel.updateSelectedTab();\r\n            }\r\n        }\r\n        else if (name === 'user-cancel-select-object') {\r\n\r\n        }\r\n        else if (name === 'user-add-object') {\r\n            self._requestSaveProject();\r\n        }\r\n        else if (name === 'user-change-object') {\r\n            self._requestSaveProject();\r\n        }\r\n        else if (name === 'user-delete-object') {\r\n            self._requestSaveProject();\r\n        }\r\n\r\n        // 反馈给监听者\r\n        for (const i in this._eventListenerMap) {\r\n            const listener = this._eventListenerMap[i];\r\n            if (listener.types.includes(name)) {\r\n                listener.func({\r\n                    ...opts\r\n                }, name);\r\n            }\r\n        }\r\n    }\r\n\r\n    addEventListener(name, types, func) {\r\n        if (!this._eventListenerMap[name]) {\r\n            this._eventListenerMap[name] = { types, func };\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 请求保存项目\r\n     */\r\n    _requestSaveProject() {\r\n        const self = this;\r\n\r\n        if (self.ctx.canAutoSaveProject && self.ctx.data.designer.autoSaveProject && self.ctx.enableDevViewport) {\r\n            clearTimeout(this._toSaveProjectThread);\r\n\r\n            this._toSaveProjectThread = setTimeout(() => {\r\n                self.ctx.projectStorageService.save();\r\n            }, 2000);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 显示相机朝向与XZ平面的接触面\r\n     */\r\n    _showCameraToXZPlane() {\r\n        if (this.ctx.enableDevViewport) {\r\n            const pos = (0,_util_tjs_camera_util__WEBPACK_IMPORTED_MODULE_0__.tjs_getCameraToXZPos)(this.ctx, this.ctx.camera);\r\n            this._buildCameraToXZPlane(pos);\r\n        }\r\n    }\r\n\r\n    _buildCameraToXZPlane(pos) {\r\n        const { THREE } = this.ctx.imports;\r\n\r\n        if (this.cameraToXZPlane == null) {\r\n            const size = this.ctx.data.designer.groundGrid.eachGridSize;\r\n\r\n            const plane = (0,_util_tjs_geometry_util__WEBPACK_IMPORTED_MODULE_1__.tjs_createCirclePlane)(this.ctx, size, null, {\r\n                color: 'silver',\r\n                opacity: 0.5,\r\n            });\r\n            plane.rotation.x = -Math.PI / 2;\r\n            plane.receiveShadow = true;\r\n            plane.userData = {\r\n                canSelect: false,\r\n            };\r\n            this.ctx.scene.add(plane);\r\n\r\n            this.cameraToXZPlane = plane;\r\n        }\r\n\r\n        const newPosition = new THREE.Vector3();\r\n        newPosition.set(pos[0], pos[1] + 0.5, pos[2]);\r\n        this.cameraToXZPlane.position.copy(newPosition);\r\n    }\r\n}\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/ability/GetUserChangeAnyEventAbility.js?\n}");

/***/ }),

/***/ "./src/ability/MoveSelectedCGObjectAbility.js":
/*!****************************************************!*\
  !*** ./src/ability/MoveSelectedCGObjectAbility.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MoveSelectedCGObjectAbility: () => (/* binding */ MoveSelectedCGObjectAbility)\n/* harmony export */ });\n/* harmony import */ var _model_CGObject__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../model/CGObject */ \"./src/model/CGObject.js\");\n/* harmony import */ var _util_game_grid_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/game-grid-util */ \"./src/util/game-grid-util.js\");\n\r\n\r\n\r\n/**\r\n * 移动选中的游戏对象能力\r\n * 拖动对象在xz轴上移动\r\n */\r\nclass MoveSelectedCGObjectAbility extends _model_CGObject__WEBPACK_IMPORTED_MODULE_0__.CGObject {\r\n\r\n    /**\r\n     * \r\n     * @param {*} ctx \r\n     * @param {*} data \r\n     */\r\n    constructor(ctx, data) {\r\n        super(ctx, data);\r\n    }\r\n\r\n    /**\r\n     * 初始化\r\n     * @param {*} pars \r\n     */\r\n    init(pars) {\r\n\r\n    }\r\n\r\n    /**\r\n     * 启动\r\n     */\r\n    start(pars) {\r\n        const self = this;\r\n        const { cgWorld } = pars;\r\n        const { THREE } = this.ctx.imports;\r\n        const userControlsAbility = cgWorld.getAbility('user_ctrl');\r\n\r\n        // 鼠标按下时的时间戳\r\n        this.startTime = 0;\r\n        // 鼠标抬起时的时间戳\r\n        this.endTime = 0;\r\n        // 鼠标移动时间\r\n        this.moveTime = 0;\r\n        // 鼠标按下时的坐标\r\n        this.mouse_2d_pos_start = new THREE.Vector2();\r\n        // 鼠标移动时的坐标\r\n        this.mouse_2d_pos_move = new THREE.Vector2();\r\n        // 鼠标按下时的在三维坐标\r\n        this.mouse_3d_pos_start;\r\n        // 鼠标按下时的对象三维坐标\r\n        this.obj_pos_start;\r\n\r\n        this.is_in_select_mode = false;\r\n\r\n        /**\r\n         * 拦截鼠标或触摸事件\r\n         * @param {MouseEvent|TouchEvent} e\r\n         */\r\n        userControlsAbility.setUserEvent('move-selected-cgo',\r\n            [\"mouseleftdown\", \"mouseup\", \"mousemove\", \"touchstart\", \"touchend\", \"touchmove\"], (e, type) => {\r\n                // 鼠标按下\r\n                if (type.startsWith('mouseleftdown')) {\r\n                    // 将鼠标位置转换为标准化设备坐标（NDC）\r\n                    self.mouse_2d_pos_start.x = (e.clientX / window.innerWidth) * 2 - 1;\r\n                    self.mouse_2d_pos_start.y = -(e.clientY / window.innerHeight) * 2 + 1;\r\n                }\r\n                else if (type.startsWith('touchstart')) {\r\n                    const touch = e.touches[0];\r\n                    // 将触摸位置转换为标准化设备坐标（NDC）\r\n                    self.mouse_2d_pos_start.x = (touch.clientX / window.innerWidth) * 2 - 1;\r\n                    self.mouse_2d_pos_start.y = -(touch.clientY / window.innerHeight) * 2 + 1;\r\n                }\r\n\r\n                // 鼠标抬起\r\n                if (type.startsWith('mouseup') || type.startsWith('touchend')) {\r\n                    self.endTime = Date.now();\r\n                    // 恢复视角控制\r\n                    const fpc = self.ctx.cgWorld.getAbility('first_person_controls');\r\n                    fpc.active();\r\n                }\r\n\r\n                if (self.startTime > self.endTime) {\r\n                    // 鼠标移动\r\n                    if (type.startsWith('mousemove')) {\r\n                        // 将鼠标位置转换为标准化设备坐标（NDC）\r\n                        self.mouse_2d_pos_move.x = (e.clientX / window.innerWidth) * 2 - 1;\r\n                        self.mouse_2d_pos_move.y = -(e.clientY / window.innerHeight) * 2 + 1;\r\n                    }\r\n                    else if (type.startsWith('touchmove')) {\r\n                        const touch = e.touches[0];\r\n                        // 将触摸位置转换为标准化设备坐标（NDC）\r\n                        self.mouse_2d_pos_move.x = (touch.clientX / window.innerWidth) * 2 - 1;\r\n                        self.mouse_2d_pos_move.y = -(touch.clientY / window.innerHeight) * 2 + 1;\r\n                    }\r\n\r\n                    if (type.endsWith('move')) {\r\n                        self.moveTime = Date.now();\r\n                    }\r\n                }\r\n\r\n                // 鼠标按下\r\n                if (type.endsWith('down') || type.endsWith('start')) {\r\n                    self.startTime = Date.now();\r\n\r\n                    // 指向对象就是选中物体就暂停视角控制\r\n                    const objs = this.ctx.cgWorld.getSelectedObjects();\r\n                    if (objs && objs.length > 0) {\r\n                        // 当前指向的对象\r\n                        const nowPointingAtCgo = this.ctx.cgWorld.getAbility('select-cgo-dev').nowPointingAtCgo;\r\n                        for (const obj of objs) {\r\n                            if (nowPointingAtCgo && nowPointingAtCgo.getId() === obj.getId()) {\r\n                                // 记录三维位置\r\n                                self.mouse_3d_pos_start = self._calTgtXZNewPos(self.ctx.camera, self.mouse_2d_pos_start, self.ctx.data.designer.focusGroundHeight);\r\n                                self.obj_pos_start = obj.getPos();\r\n                                // 暂停视角控制\r\n                                const fpc = this.ctx.cgWorld.getAbility('first_person_controls');\r\n                                fpc.deactive();\r\n                                break;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            });\r\n    }\r\n\r\n    /**\r\n     * 更新（在动画循环中调用）\r\n     */\r\n    update() {\r\n        const self = this;\r\n        // 开始拖动对象\r\n        if (self.startTime > self.endTime && Date.now() - self.moveTime < 50) {\r\n            const objs = self.ctx.cgWorld.getSelectedObjects();\r\n            if (objs && objs.length > 0) {\r\n                self.is_in_select_mode = true;\r\n                // 当前指向的对象\r\n                const nowPointingAtCgo = self.ctx.cgWorld.getAbility('select-cgo-dev').nowPointingAtCgo;\r\n                for (const obj of objs) {\r\n                    if (nowPointingAtCgo && nowPointingAtCgo.getId() === obj.getId()) {\r\n                        // 移动对象\r\n                        const newPos = self._calTgtXZNewPos(self.ctx.camera, self.mouse_2d_pos_move, self.ctx.data.designer.focusGroundHeight);\r\n                        if (newPos) {\r\n                            const offset = { x: 0, y: 0, z: 0 };\r\n                            offset.x = newPos[0] - self.mouse_3d_pos_start[0];\r\n                            offset.z = newPos[2] - self.mouse_3d_pos_start[2];\r\n\r\n                            const finalNewPos = [self.obj_pos_start[0] + offset.x, self.obj_pos_start[1] + offset.y, self.obj_pos_start[2] + offset.z];\r\n                            const finalNewPos1 = self._getGridRealPosByRealPos(finalNewPos);\r\n                            // console.log(finalNewPos1)\r\n\r\n                            finalNewPos1.y = self.obj_pos_start[1];\r\n                            obj.setPos(finalNewPos1);\r\n                            self._touchUserMoveObject();\r\n                            self._touchUserChangeObject();\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 计算鼠标在xz面上的坐标，y是高度，已经指定好了\r\n     * @param {*} camera 当前相机\r\n     * @param {THREE.Vector2} mouse_2d_pos_move 鼠标移动坐标\r\n     * @param {*} y 高度\r\n     * @returns {[x, y, z]} 新的坐标\r\n     */\r\n    _calTgtXZNewPos(camera, mouse_2d_pos_move, y) {\r\n        const { THREE } = this.ctx.imports;\r\n\r\n        // 创建射线投射器\r\n        const raycaster = new THREE.Raycaster();\r\n\r\n        // 从相机位置向鼠标位置发射射线\r\n        raycaster.setFromCamera(mouse_2d_pos_move, camera);\r\n\r\n        // 创建一个在指定高度y的水平平面\r\n        const plane = new THREE.Plane(new THREE.Vector3(0, 1, 0), -y);\r\n\r\n        // 计算射线与平面的交点\r\n        const intersectPoint = new THREE.Vector3();\r\n        const intersected = raycaster.ray.intersectPlane(plane, intersectPoint);\r\n\r\n        if (intersected) {\r\n            return [intersectPoint.x, y, intersectPoint.z];\r\n        }\r\n\r\n        return null;\r\n    }\r\n\r\n    _touchUserChangeObject() {\r\n        const self = this;\r\n        clearTimeout(this._touchUserChangeObjectTimeout);\r\n        this._touchUserChangeObjectTimeout = setTimeout(() => {\r\n            self.ctx.cgWorld.getAbility('get_user_change_any_event').touchEvent('user-change-object', {});\r\n        }, 500);\r\n    }\r\n\r\n    _touchUserMoveObject() {\r\n        const self = this;\r\n        clearTimeout(this._touchUserMoveObjectTimeout);\r\n        this._touchUserMoveObjectTimeout = setTimeout(() => {\r\n            self.ctx.cgWorld.getAbility('get_user_change_any_event').touchEvent('user-move-object', {});\r\n        }, 100);\r\n    }\r\n\r\n    /**\r\n     * 获取磁吸网格坐标\r\n     */\r\n    _getGridRealPosByRealPos(pos) {\r\n        // console.log(this.getRealSize(ctx.data.designer.magneticGridSizeUnit), ctx.data.designer.groundGrid.totalGridCount, pos)\r\n        return (0,_util_game_grid_util__WEBPACK_IMPORTED_MODULE_1__.getXZGridRealPosByRealPos)(this.getRealSize(ctx.data.designer.magneticGridSizeUnit),\r\n            ctx.data.designer.groundGrid.totalGridCount, pos);\r\n    }\r\n}\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/ability/MoveSelectedCGObjectAbility.js?\n}");

/***/ }),

/***/ "./src/ability/SelectCGObjectAbility.js":
/*!**********************************************!*\
  !*** ./src/ability/SelectCGObjectAbility.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SelectCGObjectAbility: () => (/* binding */ SelectCGObjectAbility)\n/* harmony export */ });\n/**\r\n * 选中游戏对象能力\r\n */\r\nclass SelectCGObjectAbility {\r\n\r\n    /**\r\n     * \r\n     * @param {*} ctx \r\n     * @param {*} data singleSelectMode: false\r\n     */\r\n    constructor(ctx, data) {\r\n        this.ctx = ctx;\r\n        this.data = data;\r\n\r\n        // 当前指向的对象\r\n        this.nowPointingAtCgo;\r\n    }\r\n\r\n    /**\r\n     * 初始化\r\n     * @param {*} pars \r\n     */\r\n    init(pars) {\r\n\r\n    }\r\n\r\n    /**\r\n     * 启动\r\n     */\r\n    start(pars) {\r\n        const self = this;\r\n        const { cgWorld } = pars;\r\n        const { THREE } = this.ctx.imports;\r\n        const raycaster = new THREE.Raycaster();\r\n        const userControlsAbility = cgWorld.getAbility('user_ctrl');\r\n\r\n        // 鼠标按下时的时间戳\r\n        let startTime = 0;\r\n        // 鼠标按下时的坐标\r\n        const mouse_2d_pos_start = new THREE.Vector2();\r\n\r\n        /**\r\n         * 点击事件\r\n         * @param {*} e \r\n         * @param {*} type \r\n         */\r\n        const onClickFunc = (e, type) => {\r\n            // console.log('select-cgo', type, e);\r\n            const objs = self._getFirstIntersectObject(raycaster, mouse_2d_pos_start, self.ctx.camera, self.ctx.scene);\r\n            for (const obj of objs) {\r\n                if (self.isSingleSelectMode()) {\r\n                    // 单选模式，先清空所有选中\r\n                    const selectedObjs = cgWorld.getSelectedObjects({\r\n                        excludeIds: [obj.getId()]\r\n                    });\r\n                    for (const item of selectedObjs) {\r\n                        item.deactiveSelectionMode();\r\n                    }\r\n                }\r\n\r\n                // 取消选中模式\r\n                if (obj.isSelectionModeActive()) {\r\n                    obj.deactiveSelectionMode();\r\n                    \r\n                    const gucaeAbility = cgWorld.getAbility('get_user_change_any_event');\r\n                    gucaeAbility.touchEvent('user-cancel-select-object', { target: obj });\r\n                }\r\n                // 激活选中模式\r\n                else {\r\n                    obj.activeSelectionMode();\r\n                    \r\n                    const gucaeAbility = cgWorld.getAbility('get_user_change_any_event');\r\n                    gucaeAbility.touchEvent('user-select-object', { target: obj });\r\n                }\r\n\r\n                break;\r\n            }\r\n        };\r\n\r\n        /**\r\n         * 拦截鼠标或触摸事件\r\n         * @param {MouseEvent|TouchEvent} e\r\n         */\r\n        userControlsAbility.setUserEvent('select-cgo', [\"mouseleftdown\", \"mouseup\", \"touchstart\", \"touchend\"], (e, type) => {\r\n            // 鼠标按下\r\n            if (type.startsWith('mouseleftdown')) {\r\n                // 将鼠标位置转换为标准化设备坐标（NDC）\r\n                mouse_2d_pos_start.x = (e.clientX / window.innerWidth) * 2 - 1;\r\n                mouse_2d_pos_start.y = -(e.clientY / window.innerHeight) * 2 + 1;\r\n            }\r\n            else if (type.startsWith('touchstart')) {\r\n                const touch = e.touches[0];\r\n                // 将触摸位置转换为标准化设备坐标（NDC）\r\n                mouse_2d_pos_start.x = (touch.clientX / window.innerWidth) * 2 - 1;\r\n                mouse_2d_pos_start.y = -(touch.clientY / window.innerHeight) * 2 + 1;\r\n            }\r\n\r\n            // 鼠标按下\r\n            if (type.endsWith('down') || type.endsWith('start')) {\r\n                startTime = Date.now();\r\n\r\n                const objs = self._getFirstIntersectObject(raycaster, mouse_2d_pos_start, self.ctx.camera, self.ctx.scene);\r\n                if (objs && objs.length > 0) {\r\n                    for (const obj of objs) {\r\n                        self.nowPointingAtCgo = obj;\r\n                        // console.log(`设置鼠标指向对象`, obj)\r\n                        break;\r\n                    }\r\n                }\r\n                else {\r\n                    self.nowPointingAtCgo = null;\r\n                }\r\n            }\r\n            // 鼠标抬起\r\n            else {\r\n                const t = Date.now() - startTime;\r\n                if (t < 200) {\r\n                    onClickFunc(e, type);\r\n                }\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 获取第一个被射线射中物体\r\n     * @param {THREE.Raycaster} raycaster \r\n     * @param {THREE.Vector2} mouse_2d_pos_start \r\n     * @param {*} camera \r\n     * @param {*} scene \r\n     */\r\n    _getFirstIntersectObject(raycaster, mouse_2d_pos_start, camera, scene) {\r\n        const { THREE } = this.ctx.imports;\r\n\r\n        // 使用相机的射线投射\r\n        raycaster.setFromCamera(mouse_2d_pos_start, camera);\r\n\r\n        // 计算物体和射线的交点\r\n        const intersects = raycaster.intersectObjects(scene.children);\r\n        // console.log(intersects)\r\n\r\n        const list = [];\r\n        for (const item of intersects) {\r\n            let selectedCgo = item.object.userData.cgo;\r\n            if (selectedCgo == null && item.object.parent.isGroup) {\r\n                selectedCgo = item.object.parent.userData.cgo;\r\n            }\r\n            if (selectedCgo == null && item.object.parent.userData.cgo) {\r\n                selectedCgo = item.object.parent.userData.cgo;\r\n            }\r\n\r\n            if (item.object.isSky) {\r\n            }\r\n            else if (item.object.type === 'GridHelper') {\r\n            }\r\n            else if (item.object.type === 'BoxHelper') {\r\n            }\r\n            else if (item.object.type === 'AxesHelper') {\r\n            }\r\n            else if (item.object.type === 'CameraHelper') {\r\n            }\r\n            else if (item.object.userData != null && item.object.userData.canSelect === false) {\r\n                // 标记不能选中\r\n            }\r\n            else if (selectedCgo != null && selectedCgo.data.canSelect === false) {\r\n                // 标记不能选中\r\n            }\r\n            else if (selectedCgo != null) {\r\n                list.push(selectedCgo);\r\n            }\r\n            else {\r\n                console.log('没有cgo', item);\r\n            }\r\n        }\r\n        return list;\r\n    }\r\n\r\n    /**\r\n     * 是否为单选模式\r\n     */\r\n    isSingleSelectMode() {\r\n        if (this.data.singleSelectMode == null) {\r\n            this.data.singleSelectMode = false;\r\n        }\r\n        return this.data.singleSelectMode;\r\n    }\r\n}\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/ability/SelectCGObjectAbility.js?\n}");

/***/ }),

/***/ "./src/ability/UserControlsAbility.js":
/*!********************************************!*\
  !*** ./src/ability/UserControlsAbility.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserControlsAbility: () => (/* binding */ UserControlsAbility)\n/* harmony export */ });\n/**\r\n * 用户控制能力\r\n * 通过鼠标键盘触摸屏来控制\r\n */\r\nclass UserControlsAbility {\r\n\r\n    constructor(ctx, data) {\r\n        this.ctx = ctx;\r\n        this.data = data;\r\n\r\n        this._userEventMap = {};\r\n        this._mouseDownTime = null;\r\n    }\r\n\r\n    /**\r\n     * 初始化\r\n     * @param {*} pars \r\n     */\r\n    init(pars) {\r\n        const self = this;\r\n\r\n        // 定义事件【pc端】\r\n        self._mousedown = function (e) {\r\n            if (self.debug) {\r\n                console.log(`【调试】mousedown -> ${e}`);\r\n                console.log(e);\r\n            }\r\n            for (let key in self._userEventMap) {\r\n                const event = self._userEventMap[key];\r\n\r\n                if (event.types.indexOf(\"mousedown\") !== -1 &&\r\n                    event.func instanceof Function) {\r\n                    event.func(e, 'mousedown');\r\n                }\r\n\r\n                // 鼠标左键按下\r\n                if (e.button === 0) {\r\n                    if (event.types.indexOf(\"mouseleftdown\") !== -1 &&\r\n                        event.func instanceof Function) {\r\n                        event.func(e, 'mouseleftdown');\r\n                    }\r\n                }\r\n                // 鼠标中键按下\r\n                else if (e.button === 1) {\r\n                    if (event.types.indexOf(\"mousemiddledown\") !== -1 &&\r\n                        event.func instanceof Function) {\r\n                        event.func(e, 'mousemiddledown');\r\n                    }\r\n                }\r\n                // 鼠标右键按下\r\n                else if (e.button === 2) {\r\n                    if (event.types.indexOf(\"mouserightdown\") !== -1 &&\r\n                        event.func instanceof Function) {\r\n                        event.func(e, 'mouserightdown');\r\n                    }\r\n                }\r\n\r\n            }\r\n        }\r\n\r\n        self._mouseup = function (e) {\r\n            if (self.debug) {\r\n                console.log(`【调试】mouseup -> ${e}`);\r\n                console.log(e);\r\n            }\r\n            for (let key in self._userEventMap) {\r\n                const event = self._userEventMap[key];\r\n                if (event.types.indexOf(\"mouseup\") !== -1 &&\r\n                    event.func instanceof Function) {\r\n                    event.func(e, 'mouseup');\r\n                }\r\n            }\r\n        }\r\n\r\n        self._mousemove = function (e) {\r\n            for (let key in self._userEventMap) {\r\n                const event = self._userEventMap[key];\r\n                if (event.types.indexOf(\"mousemove\") !== -1 &&\r\n                    event.func instanceof Function) {\r\n                    event.func(e, 'mousemove');\r\n                }\r\n            }\r\n        }\r\n\r\n        self._keydown = function (e) {\r\n            if (self.debug) {\r\n                console.log(`【调试】keydown -> ${e}`);\r\n                console.log(e);\r\n            }\r\n            for (let key in self._userEventMap) {\r\n                const event = self._userEventMap[key];\r\n                if (event.types.indexOf(\"keydown\") !== -1 &&\r\n                    event.func instanceof Function) {\r\n                    event.func(e, 'keydown');\r\n                }\r\n            }\r\n        }\r\n\r\n        self._keyup = function (e) {\r\n            if (self.debug) {\r\n                console.log(`【调试】keyup -> ${e}`);\r\n                console.log(e);\r\n            }\r\n            for (let key in self._userEventMap) {\r\n                const event = self._userEventMap[key];\r\n                if (event.types.indexOf(\"keyup\") !== -1 &&\r\n                    event.func instanceof Function) {\r\n                    event.func(e, 'keyup');\r\n                }\r\n            }\r\n        }\r\n\r\n        // 定义事件【移动端】\r\n        self._touchstart = function (e) {\r\n            if (self.debug) {\r\n                console.log(`【调试】touchstart -> ${e}`);\r\n                console.log(e);\r\n            }\r\n            for (let key in self._userEventMap) {\r\n                const event = self._userEventMap[key];\r\n                if (event.types.indexOf(\"touchstart\") !== -1 &&\r\n                    event.func instanceof Function) {\r\n                    event.func(e, 'touchstart');\r\n                }\r\n            }\r\n        }\r\n\r\n        self._touchmove = function (e) {\r\n            if (self.debug) {\r\n                console.log(`【调试】touchmove -> ${e}`);\r\n                console.log(e);\r\n            }\r\n            for (let key in self._userEventMap) {\r\n                const event = self._userEventMap[key];\r\n                if (event.types.indexOf(\"touchmove\") !== -1 &&\r\n                    event.func instanceof Function) {\r\n                    event.func(e, 'touchmove');\r\n                }\r\n            }\r\n        }\r\n\r\n        self._touchend = function (e) {\r\n            if (self.debug) {\r\n                console.log(`【调试】touchend -> ${e}`);\r\n                console.log(e);\r\n            }\r\n            for (let key in self._userEventMap) {\r\n                const event = self._userEventMap[key];\r\n                if (event.types.indexOf(\"touchend\") !== -1 &&\r\n                    event.func instanceof Function) {\r\n                    event.func(e, 'touchend');\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 启动\r\n     */\r\n    start() {\r\n        let el = document.getElementById('gameCanvas');\r\n        // 拦截事件【pc端】\r\n        el.addEventListener(\r\n            \"mousedown\",\r\n            this._mousedown,\r\n            false\r\n        );\r\n        el.addEventListener(\r\n            \"mouseup\",\r\n            this._mouseup,\r\n            false\r\n        );\r\n        el.addEventListener(\r\n            \"mousemove\",\r\n            this._mousemove,\r\n            false\r\n        );\r\n        el.addEventListener(\r\n            \"keydown\",\r\n            this._keydown,\r\n            false\r\n        );\r\n        el.addEventListener(\r\n            \"keyup\",\r\n            this._keyup,\r\n            false\r\n        );\r\n        // 拦截事件【移动端】\r\n        el.addEventListener(\r\n            \"touchstart\",\r\n            this._touchstart,\r\n            false\r\n        );\r\n        el.addEventListener(\r\n            \"touchmove\",\r\n            this._touchmove,\r\n            false\r\n        );\r\n        el.addEventListener(\r\n            \"touchend\",\r\n            this._touchend,\r\n            false\r\n        );\r\n    }\r\n\r\n    /**\r\n     * 注册用户事件\r\n     * @param key {string}\r\n     * @param types {string[]} 例：[\"mousedown\", \"mouseup\", \"mousemove\"]\r\n     * @param func {function}\r\n     */\r\n    setUserEvent(key, types, func) {\r\n        if (this.debug) {\r\n            console.log(`【调试】注册用户事件 -> ${key}`);\r\n        }\r\n\r\n        if (this._userEventMap[key] !== undefined) {\r\n            throw new Error(`重复注册用户事件 -> ${key}`);\r\n        }\r\n        else {\r\n            this._userEventMap[key] = {\r\n                types: types,\r\n                func: func\r\n            };\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 删除用户事件\r\n     * @param key\r\n     */\r\n    deleteUserEvent(key) {\r\n        if (this.debug) {\r\n            console.log(`【调试】删除用户事件 -> ${key}`);\r\n        }\r\n        delete this._userEventMap[key];\r\n    }\r\n\r\n    /**\r\n     * 取消事件拦截\r\n     */\r\n    deactive() {\r\n        // 取消事件拦截【pc端】\r\n        document.removeEventListener(\r\n            \"mousedown\",\r\n            this._mousedown,\r\n            false\r\n        );\r\n        document.removeEventListener(\r\n            \"mouseup\",\r\n            this._mouseup,\r\n            false\r\n        );\r\n        document.removeEventListener(\r\n            \"mousemove\",\r\n            this._mousemove,\r\n            false\r\n        );\r\n        document.removeEventListener(\r\n            \"keydown\",\r\n            this._keydown,\r\n            false\r\n        );\r\n        document.removeEventListener(\r\n            \"keyup\",\r\n            this._keyup,\r\n            false\r\n        );\r\n        // 取消事件拦截【移动端】\r\n        document.removeEventListener(\r\n            \"touchstart\",\r\n            this._touchstart,\r\n            false\r\n        );\r\n        document.removeEventListener(\r\n            \"touchmove\",\r\n            this._touchmove,\r\n            false\r\n        );\r\n        document.removeEventListener(\r\n            \"touchend\",\r\n            this._touchend,\r\n            false\r\n        );\r\n    }\r\n}\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/ability/UserControlsAbility.js?\n}");

/***/ }),

/***/ "./src/main-start.js":
/*!***************************!*\
  !*** ./src/main-start.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   start: () => (/* binding */ start)\n/* harmony export */ });\n/* harmony import */ var _model_CGWorldModel_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./model/CGWorldModel.js */ \"./src/model/CGWorldModel.js\");\n/* harmony import */ var _model_CGObjectModel_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./model/CGObjectModel.js */ \"./src/model/CGObjectModel.js\");\n/* harmony import */ var _model_CG2DGridModel_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./model/CG2DGridModel.js */ \"./src/model/CG2DGridModel.js\");\n/* harmony import */ var _util_web_util_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./util/web-util.js */ \"./src/util/web-util.js\");\n/* harmony import */ var _model_CGSkyModel_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./model/CGSkyModel.js */ \"./src/model/CGSkyModel.js\");\n/* harmony import */ var _model_CGLightModel_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./model/CGLightModel.js */ \"./src/model/CGLightModel.js\");\n/* harmony import */ var _util_game_grid_util_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./util/game-grid-util.js */ \"./src/util/game-grid-util.js\");\n/* harmony import */ var _ability_MoveSelectedCGObjectAbility_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ability/MoveSelectedCGObjectAbility.js */ \"./src/ability/MoveSelectedCGObjectAbility.js\");\n/* harmony import */ var _ability_FirstPersonControlsAbility_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ability/FirstPersonControlsAbility.js */ \"./src/ability/FirstPersonControlsAbility.js\");\n/* harmony import */ var _ability_SelectCGObjectAbility_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ability/SelectCGObjectAbility.js */ \"./src/ability/SelectCGObjectAbility.js\");\n/* harmony import */ var _ability_UserControlsAbility_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ability/UserControlsAbility.js */ \"./src/ability/UserControlsAbility.js\");\n/* harmony import */ var _ability_GetUserChangeAnyEventAbility_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./ability/GetUserChangeAnyEventAbility.js */ \"./src/ability/GetUserChangeAnyEventAbility.js\");\n/* harmony import */ var _mapper_ProjectStorageMapper_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./mapper/ProjectStorageMapper.js */ \"./src/mapper/ProjectStorageMapper.js\");\n/* harmony import */ var _mapper_ModelsStorageMapper_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./mapper/ModelsStorageMapper.js */ \"./src/mapper/ModelsStorageMapper.js\");\n/* harmony import */ var _view_model_ToolBarViewModel_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./view-model/ToolBarViewModel.js */ \"./src/view-model/ToolBarViewModel.js\");\n/* harmony import */ var _view_model_PropBarViewModel_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./view-model/PropBarViewModel.js */ \"./src/view-model/PropBarViewModel.js\");\n/* harmony import */ var _model_CGWallModel_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./model/CGWallModel.js */ \"./src/model/CGWallModel.js\");\n/* harmony import */ var _service_ProjectStorageService_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./service/ProjectStorageService.js */ \"./src/service/ProjectStorageService.js\");\n/* harmony import */ var _model_CGPlayerStartModel_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./model/CGPlayerStartModel.js */ \"./src/model/CGPlayerStartModel.js\");\n/* harmony import */ var _model_CGPaintingFrameModel_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./model/CGPaintingFrameModel.js */ \"./src/model/CGPaintingFrameModel.js\");\n/* harmony import */ var _model_CGBulbModel_js__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./model/CGBulbModel.js */ \"./src/model/CGBulbModel.js\");\n/* harmony import */ var _model_CGFloorModel_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./model/CGFloorModel.js */ \"./src/model/CGFloorModel.js\");\n/* harmony import */ var _model_CGHUDAxesIndicator_js__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./model/CGHUDAxesIndicator.js */ \"./src/model/CGHUDAxesIndicator.js\");\n/* harmony import */ var _model_CGWallMountedTelevisionModel_js__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./model/CGWallMountedTelevisionModel.js */ \"./src/model/CGWallMountedTelevisionModel.js\");\n/* harmony import */ var _service_SandboxConnection__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./service/SandboxConnection */ \"./src/service/SandboxConnection.js\");\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nfunction start(imports, opts = {}) {\r\n    const {\r\n        THREE,\r\n        CSS2DRenderer,\r\n        CSS2DObject,\r\n        CANNON,\r\n        CannonDebugger,\r\n    } = imports;\r\n\r\n    // 上下文\r\n    const ctx = {\r\n        imports: imports,\r\n        gameType: opts.gameType,\r\n        canAutoSaveProject: opts.canAutoSaveProject,\r\n        autoLoadProject: opts.autoLoadProject,\r\n\r\n        isReady: false,\r\n        // 调试\r\n        debug: false,\r\n        // 启用开发者视口\r\n        enableDevViewport: false,\r\n        // 工具库\r\n        utils: {},\r\n\r\n        // 默认场景\r\n        scene: null,\r\n        // 默认相机（游戏正式运行时主相机，非开发者视口的相机）\r\n        camera: null,\r\n        renderer: null,\r\n        css2dRenderer: null,\r\n\r\n\r\n        prevPerformaceTime: performance.now(),\r\n        // 渲染两帧画面之间所经过的时间\r\n        deltaTime: null,\r\n\r\n        // 游戏世界\r\n        cgWorld: null,\r\n\r\n        // 物理引擎\r\n        physWorld: null,\r\n        physicsBodies: [],\r\n\r\n        abilityTypes: {\r\n            MoveSelectedCGObjectAbility: _ability_MoveSelectedCGObjectAbility_js__WEBPACK_IMPORTED_MODULE_7__.MoveSelectedCGObjectAbility, FirstPersonControlsAbility: _ability_FirstPersonControlsAbility_js__WEBPACK_IMPORTED_MODULE_8__.FirstPersonControlsAbility, SelectCGObjectAbility: _ability_SelectCGObjectAbility_js__WEBPACK_IMPORTED_MODULE_9__.SelectCGObjectAbility,\r\n            UserControlsAbility: _ability_UserControlsAbility_js__WEBPACK_IMPORTED_MODULE_10__.UserControlsAbility, GetUserChangeAnyEventAbility: _ability_GetUserChangeAnyEventAbility_js__WEBPACK_IMPORTED_MODULE_11__.GetUserChangeAnyEventAbility,\r\n        },\r\n        objectTypes: {\r\n            CGObjectModel: _model_CGObjectModel_js__WEBPACK_IMPORTED_MODULE_1__.CGObjectModel, CGWallModel: _model_CGWallModel_js__WEBPACK_IMPORTED_MODULE_16__.CGWallModel, CGPlayerStartModel: _model_CGPlayerStartModel_js__WEBPACK_IMPORTED_MODULE_18__.CGPlayerStartModel, CGPaintingFrameModel: _model_CGPaintingFrameModel_js__WEBPACK_IMPORTED_MODULE_19__.CGPaintingFrameModel, CGBulbModel: _model_CGBulbModel_js__WEBPACK_IMPORTED_MODULE_20__.CGBulbModel, CGFloorModel: _model_CGFloorModel_js__WEBPACK_IMPORTED_MODULE_21__.CGFloorModel,\r\n            CGHUDAxesIndicator: _model_CGHUDAxesIndicator_js__WEBPACK_IMPORTED_MODULE_22__.CGHUDAxesIndicator, CGWallMountedTelevisionModel: _model_CGWallMountedTelevisionModel_js__WEBPACK_IMPORTED_MODULE_23__.CGWallMountedTelevisionModel,\r\n        },\r\n\r\n        data: {\r\n            showFPS: false,\r\n            showMemory: false,\r\n            // 能否暴露上下文\r\n            canExposeContext: false,\r\n\r\n            // 当前相机（用于设计师加载项目时保持镜头）\r\n            camera: {\r\n            },\r\n            // 项目\r\n            project: {\r\n                id: 'unknown',\r\n                name: '未知项目',\r\n            },\r\n            // 世界\r\n            world: {\r\n                // 一个单位的大小（可以等价于一米的长度是多少）\r\n                unitSize: 50,\r\n                // 重力大小\r\n                gravityUnit: 3,\r\n            },\r\n            // 玩家\r\n            player: {\r\n                startPosUnit: [0, 1.7, 0],\r\n                startLookAtPosUnit: [1, 0, 0],\r\n                heightUnit: 1.7,\r\n                moveSpeedUnit: 0.3,\r\n                fastMoveSpeedUnit: 0.6,\r\n                jumpStrengthUnit: 1,\r\n\r\n                // （调试）显示中心坐标指示器\r\n                showCenterIndicator: false,\r\n            },\r\n            // 设计师\r\n            designer: {\r\n                // 自动保存项目\r\n                autoSaveProject: true,\r\n                // 专注的地面高度\r\n                focusGroundHeight: 0,\r\n                // 磁吸网格大小（米），用于摆放物体时定位\r\n                magneticGridSizeUnit: 0.1,\r\n                // 地面网格\r\n                groundGrid: {\r\n                    eachGridSize: 5,\r\n                    totalGridCount: 11,\r\n                },\r\n            },\r\n        },\r\n    };\r\n\r\n    ctx.projectStorageMapper = new _mapper_ProjectStorageMapper_js__WEBPACK_IMPORTED_MODULE_12__.ProjectStorageMapper(ctx);\r\n    ctx.modelsStorageMapper = new _mapper_ModelsStorageMapper_js__WEBPACK_IMPORTED_MODULE_13__.ModelsStorageMapper(ctx);\r\n    ctx.projectStorageService = new _service_ProjectStorageService_js__WEBPACK_IMPORTED_MODULE_17__.ProjectStorageService(ctx);\r\n\r\n    // 动画\r\n    function animate() {\r\n        requestAnimationFrame(animate);\r\n\r\n        const time = performance.now();\r\n        const delta = (time - ctx.prevPerformaceTime) / 1000;\r\n        ctx.deltaTime = delta;\r\n\r\n        // 更新物理引擎\r\n        if (ctx.physWorld) {\r\n            // 步长，越大移动越快\r\n            ctx.physWorld.step(1 / 30);\r\n\r\n            // 同步物理体和Three.js对象\r\n            ctx.physicsBodies.forEach(({ mesh, body }) => {\r\n                mesh.position.copy(body.position);\r\n                mesh.quaternion.copy(body.quaternion);\r\n            });\r\n\r\n        }\r\n        if (ctx.cgWorld) {\r\n            ctx.cgWorld.update({});\r\n        }\r\n        if (ctx.physWorldDebugger) {\r\n            ctx.physWorldDebugger.update();\r\n        }\r\n\r\n        ctx.renderer.render(ctx.scene, ctx.camera);\r\n        ctx.css2dRenderer.render(ctx.scene, ctx.camera);\r\n\r\n        ctx.prevPerformaceTime = time;\r\n    }\r\n\r\n    // 初始化上下文\r\n    async function initContext(ctx) {\r\n        // 输出开发者提示，例如：项目保存成功\r\n        ctx.utils.outputDevTip = function (msg) {\r\n            if (ctx.propBarViewModel) {\r\n                ctx.propBarViewModel.showMessage('保存项目成功');\r\n            }\r\n            if (ctx.sandboxConnection) {\r\n                ctx.sandboxConnection.showDevTip(msg);\r\n            }\r\n        };\r\n\r\n        // 根据标准单位获取游戏内大小\r\n        ctx.utils.getRealSize = function (sizeUnit) {\r\n            return ctx.data.world.unitSize * sizeUnit;\r\n        };\r\n        ctx.utils.getRealSizeList = function (sizeUnitList) {\r\n            return sizeUnitList.map((n) => {\r\n                return ctx.data.world.unitSize * n;\r\n            });\r\n        };\r\n        ctx.utils.getUnitSize = function (realSize) {\r\n            return realSize / ctx.data.world.unitSize;\r\n        };\r\n        ctx.utils.getUnitSizeList = function (realSizeList) {\r\n            return realSizeList.map((n) => {\r\n                return n / ctx.data.world.unitSize;\r\n            });\r\n        };\r\n\r\n\r\n        ctx.utils.getDesignGroundRealPosByGridPos = function (gPos) {\r\n            return (0,_util_game_grid_util_js__WEBPACK_IMPORTED_MODULE_6__.getXZRealPosByGridPos)(ctx.data.designer.groundGrid.eachGridSize,\r\n                ctx.data.designer.groundGrid.totalGridCount, gPos);\r\n        };\r\n        ctx.utils.getDesignGroundGridPosByRealPos = function (pos) {\r\n            return (0,_util_game_grid_util_js__WEBPACK_IMPORTED_MODULE_6__.getXZGridPosByRealPos)(ctx.data.designer.groundGrid.eachGridSize,\r\n                ctx.data.designer.groundGrid.totalGridCount, pos);\r\n        };\r\n        ctx.utils.getDesignGroundGridRealPosByRealPos = function (pos) {\r\n            return (0,_util_game_grid_util_js__WEBPACK_IMPORTED_MODULE_6__.getXZGridRealPosByRealPos)(ctx.data.designer.groundGrid.eachGridSize,\r\n                ctx.data.designer.groundGrid.totalGridCount, pos);\r\n        };\r\n\r\n        ctx.utils.showFPSMonitor = function () {\r\n            (function () {\r\n                var script = document.createElement('script');\r\n                script.onload = function () {\r\n                    var stats = new Stats();\r\n                    document.body.appendChild(stats.dom);\r\n                    stats.dom.style.left = '350px'\r\n                    requestAnimationFrame(function loop() {\r\n                        stats.update();\r\n                        requestAnimationFrame(loop)\r\n                    });\r\n                };\r\n                script.src = './lib/stats/stats.min.js';\r\n                document.head.appendChild(script);\r\n            })()\r\n        };\r\n\r\n        ctx.utils.showMemoryMonitor = function () {\r\n            (function () {\r\n                var script = document.createElement('script');\r\n                script.src = './lib/memory-stats/memory-stats-start.js';\r\n                document.head.appendChild(script);\r\n            })()\r\n        };\r\n    }\r\n\r\n    // 初始化图形引擎\r\n    async function initGraphicsEngine(ctx) {\r\n        const scene = new THREE.Scene();\r\n\r\n        // const camera = new THREE.OrthographicCamera(-400, 400, 300, -300, 1, 500); // 创建正交相机（2d）\r\n        const camera = new THREE.PerspectiveCamera(50, window.innerWidth / window.innerHeight, 0.1, 100000); // 创建透视相机（3d）\r\n        camera.position.set(0, 50, 0); // 调整相机高度\r\n        camera.lookAt(90, 30, 0);\r\n\r\n        const renderer = new THREE.WebGLRenderer({ canvas: document.getElementById('gameCanvas') });\r\n        renderer.setSize(window.innerWidth, window.innerHeight);\r\n        renderer.shadowMap.enabled = true;\r\n        renderer.shadowMap.type = THREE.PCFSoftShadowMap;\r\n        document.body.appendChild(renderer.domElement);\r\n\r\n        const css2dRenderer = new CSS2DRenderer();\r\n        css2dRenderer.setSize(window.innerWidth, window.innerHeight);\r\n        css2dRenderer.domElement.style.position = 'absolute';\r\n        css2dRenderer.domElement.style.top = '0px';\r\n        css2dRenderer.domElement.style.pointerEvents = 'none'; // 解决事件被阻挡导致无法旋转相机的问题\r\n        // css2dRenderer.domElement.style.zIndex = \"2\";\r\n        document.body.appendChild(css2dRenderer.domElement);\r\n\r\n        ctx.scene = scene;\r\n        ctx.camera = camera;\r\n        ctx.renderer = renderer;\r\n        ctx.css2dRenderer = css2dRenderer;\r\n\r\n        // 自动适应窗口大小\r\n        window.addEventListener('resize', () => {\r\n            ctx.cgWorld.resize();\r\n\r\n            ctx.camera.aspect = window.innerWidth / window.innerHeight;\r\n            ctx.camera.updateProjectionMatrix();\r\n            ctx.renderer.setSize(window.innerWidth, window.innerHeight);\r\n            ctx.css2dRenderer.setSize(window.innerWidth, window.innerHeight);\r\n        });\r\n    }\r\n\r\n    // 初始化物理引擎\r\n    async function initPhysicsEngine(ctx) {\r\n        // 创建游戏世界\r\n        ctx.cgWorld = new _model_CGWorldModel_js__WEBPACK_IMPORTED_MODULE_0__.CGWorldModel(ctx);\r\n        // 创建物理世界\r\n        ctx.physWorld = new CANNON.World();\r\n        ctx.physWorld.gravity.set(0, -9.82, 0); // 设置重力\r\n        ctx.physWorld.broadphase = new CANNON.NaiveBroadphase();\r\n\r\n        // if (ctx.enableDevViewport) {\r\n        //     ctx.physWorldDebugger = new CannonDebugger(ctx.scene, ctx.physWorld, {});\r\n        // }\r\n    }\r\n\r\n    async function main(ctx) {\r\n        if ((0,_util_web_util_js__WEBPACK_IMPORTED_MODULE_3__.queryString)('debug') === '1') {\r\n            ctx.debug = true;\r\n        }\r\n        if (opts.dev || (0,_util_web_util_js__WEBPACK_IMPORTED_MODULE_3__.queryString)('dev') === '1') {\r\n            ctx.enableDevViewport = true;\r\n            ctx.sandboxConnection = new _service_SandboxConnection__WEBPACK_IMPORTED_MODULE_24__.SandboxConnection(ctx);\r\n            window.sandboxConnection = ctx.sandboxConnection;\r\n        }\r\n        const t_project_id = (0,_util_web_util_js__WEBPACK_IMPORTED_MODULE_3__.queryString)('pjt');\r\n        if (t_project_id && t_project_id.length > 0) {\r\n            ctx.data.project.id = t_project_id;\r\n        }\r\n\r\n        if (ctx.autoLoadProject !== false) {\r\n            // 加载项目\r\n            await ctx.projectStorageService.load(opts);\r\n        }\r\n\r\n        if (opts.beforeMainStartItems) {\r\n            for (const item of opts.beforeMainStartItems) {\r\n                if (item.onlyDevViewport && !ctx.enableDevViewport) {\r\n                }\r\n                else if (item.onlyViewport && ctx.enableDevViewport) {\r\n                }\r\n                else if (item.type === 'ctx-init') {\r\n                    for (const prop in item.data) {\r\n                        ctx[prop] = item.data[prop];\r\n                    }\r\n                }\r\n                else if (item.type === 'camera-init') {\r\n                    for (const prop in item.data) {\r\n                        ctx.data.camera[prop] = item.data[prop];\r\n                    }\r\n                }\r\n                else if (item.type === 'player-init') {\r\n                    for (const prop in item.data) {\r\n                        ctx.data.player[prop] = item.data[prop];\r\n                    }\r\n                }\r\n                else if (item.type === 'world-init') {\r\n                    if (item.data.unitSize >= 0) ctx.data.world.unitSize = item.data.unitSize;\r\n                    if (item.data.gravityUnit >= 0) ctx.data.world.gravityUnit = item.data.gravityUnit;\r\n                }\r\n            }\r\n        }\r\n\r\n        // *** 初始上下文 ***\r\n        await initContext(ctx);\r\n\r\n        // *** 初始化图形引擎 ***\r\n        await initGraphicsEngine(ctx);\r\n\r\n        // *** 初始化物理引擎 ***\r\n        await initPhysicsEngine(ctx);\r\n\r\n\r\n\r\n        if (opts.afterMainStartItems) {\r\n            for (const item of opts.afterMainStartItems) {\r\n                if (item.onlyDevViewport && !ctx.enableDevViewport) {\r\n                }\r\n                else if (item.data.__onlyDevViewport && !ctx.enableDevViewport) {\r\n                }\r\n                else if (item.onlyViewport && ctx.enableDevViewport) {\r\n                }\r\n                else if (item.data.__onlyViewport && ctx.enableDevViewport) {\r\n                }\r\n                else if (item.type === 'world-grid-build') {\r\n                    ctx.data.designer.groundGrid.eachGridSize = ctx.utils.getRealSize(item.data.eachGridSizeUnit);\r\n                    ctx.data.designer.groundGrid.totalGridCount = item.data.totalGridCount;\r\n                    await ctx.cgWorld.addObject(new _model_CG2DGridModel_js__WEBPACK_IMPORTED_MODULE_2__.CG2DGridModel(ctx, item.data));\r\n                }\r\n                else if (item.type === 'object-build') {\r\n                    await ctx.cgWorld.addObject(new _model_CGObjectModel_js__WEBPACK_IMPORTED_MODULE_1__.CGObjectModel(ctx, item.data));\r\n                }\r\n                else if (item.type === 'sky-object-build') {\r\n                    await ctx.cgWorld.addObject(new _model_CGSkyModel_js__WEBPACK_IMPORTED_MODULE_4__.CGSkyModel(ctx, item.data));\r\n                }\r\n                else if (item.type === 'light-object-build') {\r\n                    await ctx.cgWorld.addObject(new _model_CGLightModel_js__WEBPACK_IMPORTED_MODULE_5__.CGLightModel(ctx, item.data));\r\n                }\r\n                else if (item.type.endsWith('-ability-build')) {\r\n                    let typeName = item.type.replace('-ability-build', '');\r\n                    if (ctx.abilityTypes[typeName] == null) {\r\n                        alert(`未找到能力类型：${typeName}`);\r\n                    }\r\n                    else {\r\n                        await ctx.cgWorld.addAbility(new ctx.abilityTypes[typeName](ctx, item.data), item.name);\r\n                    }\r\n                }\r\n                else if (item.type.endsWith('-object-build')) {\r\n                    let typeName = item.type.replace('-object-build', '');\r\n                    if (ctx.objectTypes[typeName] == null) {\r\n                        alert(`未找到对象类型：${typeName}`);\r\n                    }\r\n                    else {\r\n                        await ctx.cgWorld.addObject(new ctx.objectTypes[typeName](ctx, item.data));\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        await ctx.cgWorld.startAbilites();\r\n        await ctx.cgWorld.buildObjects();\r\n\r\n        if (ctx.canExposeContext) {\r\n            window.ctx = ctx;\r\n        }\r\n        // 显示实时FPS数据\r\n        if (ctx.showFPS) {\r\n            ctx.utils.showMemoryMonitor()\r\n        }\r\n        // 显示实时内存数据\r\n        if (ctx.showMemory) {\r\n            ctx.utils.showMemoryMonitor()\r\n        }\r\n\r\n        // 开发视图\r\n        if (ctx.enableDevViewport) {\r\n            // 实时坐标指示器\r\n            const topAxesIndicator = new _model_CGHUDAxesIndicator_js__WEBPACK_IMPORTED_MODULE_22__.CGHUDAxesIndicator(ctx, {\r\n                __autoSave: false,\r\n            });\r\n            await ctx.cgWorld.addObjectAndBuild(topAxesIndicator);\r\n\r\n            // 创建工具栏、属性栏\r\n            if (opts.editor) {\r\n                ctx.toolBarViewModel = new _view_model_ToolBarViewModel_js__WEBPACK_IMPORTED_MODULE_14__.ToolBarViewModel(ctx);\r\n                ctx.toolBarViewModel.build();\r\n                ctx.propBarViewModel = new _view_model_PropBarViewModel_js__WEBPACK_IMPORTED_MODULE_15__.PropBarViewModel(ctx);\r\n                ctx.propBarViewModel.build();\r\n            }\r\n        }\r\n\r\n        ctx.isReady = true;\r\n\r\n        console.log(`玩家身高：${ctx.data.player.heightUnit}`);\r\n        console.log(`玩家移动速度：${ctx.data.player.moveSpeedUnit}`);\r\n\r\n        animate();\r\n    }\r\n\r\n    main(ctx);\r\n}\r\n\r\n__webpack_require__.g.start = start;\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/main-start.js?\n}");

/***/ }),

/***/ "./src/mapper/ModelsStorageMapper.js":
/*!*******************************************!*\
  !*** ./src/mapper/ModelsStorageMapper.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ModelsStorageMapper: () => (/* binding */ ModelsStorageMapper)\n/* harmony export */ });\n/* harmony import */ var _util_http_util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/http-util.js */ \"./src/util/http-util.js\");\n\r\n/**\r\n * 模型库存储\r\n */\r\nclass ModelsStorageMapper {\r\n    constructor(ctx) {\r\n        this.ctx = ctx;\r\n    }\r\n\r\n    async loadList() {\r\n        const res = await (0,_util_http_util_js__WEBPACK_IMPORTED_MODULE_0__.reqPostJson)('/api/game-editor/models/loadList', {\r\n            gameType: this.ctx.gameType,\r\n        })\r\n        return res.data;\r\n    }\r\n}\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/mapper/ModelsStorageMapper.js?\n}");

/***/ }),

/***/ "./src/mapper/ProjectStorageMapper.js":
/*!********************************************!*\
  !*** ./src/mapper/ProjectStorageMapper.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectStorageMapper: () => (/* binding */ ProjectStorageMapper)\n/* harmony export */ });\n/* harmony import */ var _util_http_util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/http-util.js */ \"./src/util/http-util.js\");\n\r\n/**\r\n * 项目存储\r\n */\r\nclass ProjectStorageMapper {\r\n    constructor(ctx) {\r\n        this.ctx = ctx;\r\n    }\r\n\r\n    async save(id, name, data) {\r\n        (0,_util_http_util_js__WEBPACK_IMPORTED_MODULE_0__.reqPostJson)('/api/game-editor/project/save', {\r\n            id,\r\n            name,\r\n            data,\r\n        })\r\n    }\r\n\r\n    async load(id) {\r\n        const res = await (0,_util_http_util_js__WEBPACK_IMPORTED_MODULE_0__.reqPostJson)('/api/game-editor/project/load', {\r\n            id,\r\n        })\r\n        return res.data;\r\n    }\r\n}\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/mapper/ProjectStorageMapper.js?\n}");

/***/ }),

/***/ "./src/model/CG2DGridModel.js":
/*!************************************!*\
  !*** ./src/model/CG2DGridModel.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CG2DGridModel: () => (/* binding */ CG2DGridModel)\n/* harmony export */ });\n/* harmony import */ var _CGObjectModel_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./CGObjectModel.js */ \"./src/model/CGObjectModel.js\");\n\r\n/**\r\n * 2d网格\r\n */\r\nclass CG2DGridModel extends _CGObjectModel_js__WEBPACK_IMPORTED_MODULE_0__.CGObjectModel {\r\n\r\n    constructor(ctx, data = {}) {\r\n        super(ctx, data);\r\n\r\n        if (data.canSelect == null) data.canSelect = false;\r\n    }\r\n\r\n    build() {\r\n        const self = this;\r\n        const { THREE } = this.ctx.imports;\r\n        // 鼠标移动时的坐标\r\n        this.mouse_2d_pos_move = new THREE.Vector2();\r\n\r\n        let eachGridSize = this.data.eachGridSize || this.ctx.data.designer.groundGrid.eachGridSize;\r\n        let totalGridCount = this.data.totalGridCount || this.ctx.data.designer.groundGrid.totalGridCount;\r\n        let colorCenterLine = this.data.colorCenterLine || 'red';\r\n        let colorGrid = this.data.colorGrid || 'silver';\r\n\r\n        if (this.gridHelper == null) {\r\n            this.gridHelper = new THREE.GridHelper(eachGridSize * totalGridCount, totalGridCount,\r\n                colorCenterLine, colorGrid);\r\n            this.ctx.scene.add(this.gridHelper);\r\n        }\r\n        if (this.data.pos) {\r\n            this.gridHelper.position.set(this.data.pos[0], this.data.pos[1], this.data.pos[2]);\r\n        }\r\n        this.model = this.gridHelper;\r\n\r\n        if (this.userControlsAbility == null) {\r\n            this.userControlsAbility = this.ctx.cgWorld.getAbility('user_ctrl');\r\n\r\n            this.userControlsAbility.setUserEvent(`${this.getId()}|CG2DGridModel`,\r\n                [\"mousemove\"], (e, type) => {\r\n                    // 鼠标移动\r\n                    if (type.startsWith('mousemove')) {\r\n                        // 将鼠标位置转换为标准化设备坐标（NDC）\r\n                        self.mouse_2d_pos_move.x = (e.clientX / window.innerWidth) * 2 - 1;\r\n                        self.mouse_2d_pos_move.y = -(e.clientY / window.innerHeight) * 2 + 1;\r\n\r\n                        const newPos = self._calTgtXZNewPos(self.ctx.camera, self.mouse_2d_pos_move, 0);\r\n                        if (newPos != null) {\r\n                            const gridPos = self.ctx.utils.getDesignGroundGridPosByRealPos(newPos);\r\n\r\n                            const gridCenterPos = this.ctx.utils.getDesignGroundRealPosByGridPos(gridPos)\r\n                            gridCenterPos.y = this.gridHelper.position.y;\r\n                            self._showCursorGridPlane(gridCenterPos);\r\n                        }\r\n                    }\r\n                });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 显示鼠标所在网格面\r\n     * @param {*} gPos \r\n     * @param {*} y \r\n     */\r\n    _showCursorGridPlane(pos) {\r\n        if (this.cursorHoverPlane == null) {\r\n            const { THREE } = this.ctx.imports;\r\n            const size = this.ctx.data.designer.groundGrid.eachGridSize;\r\n            const geometry = new THREE.PlaneGeometry(size, size);\r\n            const material = new THREE.MeshStandardMaterial({\r\n                color: '#7F7F7',\r\n                side: THREE.DoubleSide, // 两面都显示\r\n                transparent: true,\r\n                opacity: 0.3,\r\n            });\r\n            const plane = new THREE.Mesh(geometry, material);\r\n            plane.rotation.x = -Math.PI / 2;\r\n            plane.position.y = this.gridHelper.position.y;\r\n            plane.receiveShadow = true;\r\n            plane.userData = {\r\n                cgo: this,\r\n            }\r\n            this.ctx.scene.add(plane);\r\n\r\n            this.cursorHoverPlane = plane;\r\n        }\r\n\r\n        this.cursorHoverPlane.position.copy(pos);\r\n    }\r\n\r\n    /**\r\n     * 计算鼠标在xz面上的坐标，y是高度，已经指定好了\r\n     * @param {*} camera 当前相机\r\n     * @param {THREE.Vector2} mouse_2d_pos_move 鼠标移动坐标\r\n     * @param {*} y 高度\r\n     * @returns {[x, y, z]} 新的坐标\r\n     */\r\n    _calTgtXZNewPos(camera, mouse_2d_pos_move, y) {\r\n        const { THREE } = this.ctx.imports;\r\n\r\n        // 创建射线投射器\r\n        const raycaster = new THREE.Raycaster();\r\n\r\n        // 从相机位置向鼠标位置发射射线\r\n        raycaster.setFromCamera(mouse_2d_pos_move, camera);\r\n\r\n        // 创建一个在指定高度y的水平平面\r\n        const plane = new THREE.Plane(new THREE.Vector3(0, 1, 0), -y);\r\n\r\n        // 计算射线与平面的交点\r\n        const intersectPoint = new THREE.Vector3();\r\n        const intersected = raycaster.ray.intersectPlane(plane, intersectPoint);\r\n\r\n        if (intersected) {\r\n            return [intersectPoint.x, y, intersectPoint.z];\r\n        }\r\n\r\n        return null;\r\n    }\r\n}\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/model/CG2DGridModel.js?\n}");

/***/ }),

/***/ "./src/model/CGBulbModel.js":
/*!**********************************!*\
  !*** ./src/model/CGBulbModel.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CGBulbModel: () => (/* binding */ CGBulbModel)\n/* harmony export */ });\n/* harmony import */ var _util_tjs_light_util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/tjs-light-util */ \"./src/util/tjs-light-util.js\");\n/* harmony import */ var _CGObjectModel__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CGObjectModel */ \"./src/model/CGObjectModel.js\");\n\r\n\r\n/**\r\n * 灯泡模型\r\n */\r\nclass CGBulbModel extends _CGObjectModel__WEBPACK_IMPORTED_MODULE_1__.CGObjectModel {\r\n    constructor(ctx, data) {\r\n        super(ctx, data);\r\n\r\n        if (data.intensity == null) data.intensity = 3; // 强度\r\n    }\r\n\r\n    async build() {\r\n        if (this.model == null) {\r\n            this.model = (0,_util_tjs_light_util__WEBPACK_IMPORTED_MODULE_0__.tjs_createBulb)(this.ctx, {});\r\n            this.ctx.scene.add(this.model);\r\n        }\r\n\r\n        this.model.getObjectByName('bulbLight').intensity = this.data.intensity;\r\n\r\n        // if (this.pointLightHelper) {\r\n        //     this.pointLightHelper.update();\r\n        // }\r\n        // else {\r\n        //     this.pointLightHelper = tjs_createPointLightHelper(this.ctx, this.model.getObjectByName('bulbLight'));\r\n\r\n        //     this.pointLightHelper.userData.cgo = this;\r\n        //     this.ctx.scene.add(this.pointLightHelper);\r\n        // }\r\n\r\n        await super.build();\r\n    }\r\n\r\n    static getProps(opts = {}) {\r\n        const list = [];\r\n        list.push(...[\r\n            { label: '强度', name: 'intensity', type: 'Number', default: 0, step: 0.5 },\r\n        ]);\r\n        if (opts.onlySelf !== true) {\r\n            list.push(...super.getProps());\r\n        }\r\n        return list;\r\n    }\r\n}\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/model/CGBulbModel.js?\n}");

/***/ }),

/***/ "./src/model/CGFloorModel.js":
/*!***********************************!*\
  !*** ./src/model/CGFloorModel.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CGFloorModel: () => (/* binding */ CGFloorModel)\n/* harmony export */ });\n/* harmony import */ var _CGObjectModel__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./CGObjectModel */ \"./src/model/CGObjectModel.js\");\n\r\n/**\r\n * 地板模型\r\n */\r\nclass CGFloorModel extends _CGObjectModel__WEBPACK_IMPORTED_MODULE_0__.CGObjectModel {\r\n    constructor(ctx, data) {\r\n        super(ctx, data);\r\n\r\n        // 长度\r\n        if (data.sizeXUnit == null) data.sizeXUnit = 10;\r\n        // 厚度\r\n        if (data.sizeYUnit == null) data.sizeYUnit = 0.2;\r\n        // 宽度\r\n        if (data.sizeZUnit == null) data.sizeZUnit = 6;\r\n\r\n        data.modelCreator = {\r\n            type: 'cube',\r\n        };\r\n\r\n        data.collidableObject = {};\r\n    }\r\n\r\n    async build() {\r\n        await super.build();\r\n    }\r\n\r\n    async setPropValue(name, value) {\r\n        const list = this.constructor.getProps({ onlySelf: true });\r\n        const prop = list.find(item => item.name == name);\r\n        if (prop) {\r\n            this.data[name] = value;\r\n            this.destroy();\r\n            await this.build();\r\n            return true;\r\n        }\r\n        else {\r\n            return await super.setPropValue(name, value);\r\n        }\r\n    }\r\n\r\n    static getProps(opts = {}) {\r\n        const list = [];\r\n        list.push(...[\r\n            { label: '长度（米）', name: 'sizeXUnit', type: 'Number' },\r\n            { label: '厚度（米）', name: 'sizeYUnit', type: 'Number' },\r\n            { label: '宽度（米）', name: 'sizeZUnit', type: 'Number' },\r\n        ]);\r\n        if (opts.onlySelf !== true) {\r\n            list.push(...super.getProps());\r\n        }\r\n        return list;\r\n    }\r\n}\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/model/CGFloorModel.js?\n}");

/***/ }),

/***/ "./src/model/CGHUDAxesIndicator.js":
/*!*****************************************!*\
  !*** ./src/model/CGHUDAxesIndicator.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CGHUDAxesIndicator: () => (/* binding */ CGHUDAxesIndicator)\n/* harmony export */ });\n/* harmony import */ var _util_tjs_axes_util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/tjs-axes-util */ \"./src/util/tjs-axes-util.js\");\n/* harmony import */ var _util_tjs_camera_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/tjs-camera-util */ \"./src/util/tjs-camera-util.js\");\n/* harmony import */ var _CGObjectModel__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CGObjectModel */ \"./src/model/CGObjectModel.js\");\n\r\n\r\n\r\n/**\r\n * 顶部坐标指示器（帮助用户知道当前方向坐标朝向）\r\n */\r\nclass CGHUDAxesIndicator extends _CGObjectModel__WEBPACK_IMPORTED_MODULE_2__.CGObjectModel {\r\n    constructor(ctx, data) {\r\n        super(ctx, data);\r\n\r\n        this.smallWidth = 200;\r\n        this.smallHeight = 200;\r\n    }\r\n\r\n    async build() {\r\n        if (this.model == null) {\r\n            let oCamSize = 15;\r\n\r\n            const { THREE, CSS2DRenderer } = this.ctx.imports;\r\n\r\n            // 创建坐标指示器（THREE.AxesHelper）\r\n            this.model = (0,_util_tjs_axes_util__WEBPACK_IMPORTED_MODULE_0__.tjs_createAxesIndicator)(this.ctx, {\r\n                caiAxesSize: this.smallHeight * 0.05,\r\n                arrowLabelDistance: 1,\r\n                caiArrowRadius: 0.3,\r\n            });\r\n\r\n            this.hudRenderer = new THREE.WebGLRenderer({ antialias: true, logarithmicDepthBuffer: true });\r\n            this.hudRenderer.setSize(this.smallWidth, this.smallHeight);\r\n            this.hudRenderer.setClearColor(0x000000, 0); // required\r\n            this.hudRenderer.domElement.style.position = 'absolute';\r\n            this.hudRenderer.domElement.style.top = '0px';\r\n            this.hudRenderer.domElement.style.left = `${(window.innerWidth / 2) - (this.smallWidth / 2)}px`;\r\n\r\n            this.hudRenderer.domElement.style.zIndex = '999';\r\n            document.body.appendChild(this.hudRenderer.domElement);\r\n\r\n            this.hudCss2dRenderer = new CSS2DRenderer();\r\n            this.hudCss2dRenderer.setSize(this.smallWidth, this.smallHeight);\r\n            this.hudCss2dRenderer.domElement.style.position = 'absolute';\r\n            this.hudCss2dRenderer.domElement.style.top = '0px';\r\n            this.hudCss2dRenderer.domElement.style.left = `${(window.innerWidth / 2) - (this.smallWidth / 2)}px`;\r\n            this.hudCss2dRenderer.domElement.style.zIndex = '999';\r\n            document.body.appendChild(this.hudCss2dRenderer.domElement);\r\n\r\n\r\n\r\n            this.hudScene = new THREE.Scene();\r\n            this.hudCamera = new THREE.OrthographicCamera(this.smallWidth / -oCamSize, this.smallWidth / oCamSize,\r\n                this.smallHeight / oCamSize, this.smallHeight / -oCamSize, 0.01, 1000);\r\n            this.hudCamera.position.set(0, 0, 0);\r\n            this.hudScene.add(this.model);\r\n        }\r\n        await super.build();\r\n\r\n        // console.log('build', this.model)\r\n        this.resize();\r\n    }\r\n\r\n    resize() {\r\n        this.hudRenderer.domElement.style.left = `${(window.innerWidth / 2) - (this.smallWidth / 2)}px`;\r\n        this.hudCss2dRenderer.domElement.style.left = `${(window.innerWidth / 2) - (this.smallWidth / 2)}px`;\r\n    }\r\n\r\n    // 在animate中调用\r\n    update() {\r\n        // console.log('update', this.model)\r\n        if (this.model) {\r\n            this.hudCamera.rotation.copy(this.ctx.camera.rotation);\r\n\r\n            // 与中心点保持一定距离并看向中心点\r\n            const p0 = (0,_util_tjs_camera_util__WEBPACK_IMPORTED_MODULE_1__.tjs_calCameraLookAtPoint)(this.ctx, this.hudCamera, 100);\r\n            this.hudCamera.position.sub(p0);\r\n            this.hudCamera.lookAt(this.model.position);\r\n\r\n\r\n            this.hudRenderer.render(this.hudScene, this.hudCamera);\r\n            this.hudCss2dRenderer.render(this.hudScene, this.hudCamera);\r\n        }\r\n    }\r\n}\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/model/CGHUDAxesIndicator.js?\n}");

/***/ }),

/***/ "./src/model/CGLightModel.js":
/*!***********************************!*\
  !*** ./src/model/CGLightModel.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CGLightModel: () => (/* binding */ CGLightModel)\n/* harmony export */ });\n/* harmony import */ var _CGObjectModel_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./CGObjectModel.js */ \"./src/model/CGObjectModel.js\");\n\r\n/**\r\n * 光源模型\r\n */\r\nclass CGLightModel extends _CGObjectModel_js__WEBPACK_IMPORTED_MODULE_0__.CGObjectModel {\r\n    constructor(ctx, data) {\r\n        super(ctx, data);\r\n    }\r\n\r\n    build() {\r\n        const { THREE } = this.ctx.imports;\r\n        \r\n        if (this.data.ambientLight) {\r\n            // *** 环境光源 ***\r\n            if (this.ambientLight == null) {\r\n                this.ambientLight = new THREE.AmbientLight(0xffffff, this.data.intensity ?? 0.2);\r\n                this.ctx.scene.add(this.ambientLight);\r\n            }\r\n            else {\r\n                this.ambientLight.intensity = this.data.intensity ?? 0.2;\r\n            }\r\n        }\r\n    }\r\n}\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/model/CGLightModel.js?\n}");

/***/ }),

/***/ "./src/model/CGObject.js":
/*!*******************************!*\
  !*** ./src/model/CGObject.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CGObject: () => (/* binding */ CGObject)\n/* harmony export */ });\n/* harmony import */ var _util_id_util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/id-util.js */ \"./src/util/id-util.js\");\n\r\nclass CGObject {\r\n\r\n    constructor(ctx, data = {}) {\r\n        this.ctx = ctx;\r\n        this.data = data;\r\n\r\n        // ID\r\n        if (this.data.id == null) this.data.id = (0,_util_id_util_js__WEBPACK_IMPORTED_MODULE_0__.newCUIdA)();\r\n    }\r\n\r\n    getId() {\r\n        return this.data.id;\r\n    }\r\n\r\n    getPropValue(name) {\r\n        return this.data[name];\r\n    }\r\n\r\n    async setPropValue(name, value) {\r\n        const list = this.constructor.getProps();\r\n        const prop = list.find(item => item.name == name);\r\n        if (prop) {\r\n            this.data[name] = value;\r\n            return true;\r\n        }\r\n    }\r\n\r\n    static getProps() {\r\n        return [\r\n            { label: 'ID', name: 'id', type: 'String', readonly: true },\r\n        ]\r\n    }\r\n\r\n    getRealSize(sizeUnit) {\r\n        return this.ctx.utils.getRealSize(sizeUnit);\r\n    }\r\n\r\n    getRealSizeList(sizeUnitList) {\r\n        return this.ctx.utils.getRealSizeList(sizeUnitList);\r\n    }\r\n\r\n    getUnitSize(realSize) {\r\n        return this.ctx.utils.getUnitSize(realSize);\r\n    }\r\n\r\n    getUnitSizeList(realSizeList) {\r\n        return this.ctx.utils.getUnitSizeList(realSizeList);\r\n    }\r\n\r\n\r\n}\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/model/CGObject.js?\n}");

/***/ }),

/***/ "./src/model/CGObjectModel.js":
/*!************************************!*\
  !*** ./src/model/CGObjectModel.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CGObjectModel: () => (/* binding */ CGObjectModel)\n/* harmony export */ });\n/* harmony import */ var _util_import_fbx_model_util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/import-fbx-model-util.js */ \"./src/util/import-fbx-model-util.js\");\n/* harmony import */ var _part_ModelCreatorPart_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./part/ModelCreatorPart.js */ \"./src/model/part/ModelCreatorPart.js\");\n/* harmony import */ var _part_PhysBodyCreatorPart_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./part/PhysBodyCreatorPart.js */ \"./src/model/part/PhysBodyCreatorPart.js\");\n/* harmony import */ var _part_HelperCreatorPart_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./part/HelperCreatorPart.js */ \"./src/model/part/HelperCreatorPart.js\");\n/* harmony import */ var _CGObject_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CGObject.js */ \"./src/model/CGObject.js\");\n/* harmony import */ var _util_tjs_axes_util_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../util/tjs-axes-util.js */ \"./src/util/tjs-axes-util.js\");\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n/**\r\n * CS Game Object Model\r\n * 游戏对象模型\r\n */\r\nclass CGObjectModel extends _CGObject_js__WEBPACK_IMPORTED_MODULE_4__.CGObject {\r\n    constructor(ctx, data = {}) {\r\n        super(ctx, data);\r\n\r\n        // *** 数据 ***\r\n        // pos: [0, 0, 0] 初始坐标\r\n        // modelCreator: null 模型创建器\r\n        // modelLoader: null 外部模型加载器\r\n        // physBody: null 物理体\r\n        // 坐标\r\n        if (this.data.pos == null) this.data.pos = [0, 0, 0];\r\n        // 缩放比例\r\n        if (this.data.scale == null) this.data.scale = 1;\r\n        // 是否被选中\r\n        if (this.data.isSelected == null) this.data.isSelected = false;\r\n\r\n        // *** 系统参数 ***\r\n        // 可见模型\r\n        this.model = null;\r\n        // 物理体\r\n        this.physBody = null;\r\n        // 模型边界盒\r\n        this.modelBox = null;\r\n\r\n        this.moreModels = [];\r\n        // 联动对象\r\n        this.togetherItemsMap = {};\r\n        // 激活选中模式的时间戳\r\n        this.activeSelectionModeTime = 0;\r\n\r\n        this.buildCount = 0;\r\n\r\n        this.sizeX = 10;\r\n        this.sizeY = 10;\r\n        this.sizeZ = 10;\r\n    }\r\n\r\n    async build() {\r\n        const { THREE } = this.ctx.imports;\r\n\r\n        if (this.data.sizeXUnit >= 0) {\r\n            this.sizeX = this.data.sizeXUnit * this.ctx.data.world.unitSize;\r\n        }\r\n        if (this.data.sizeYUnit >= 0) {\r\n            this.sizeY = this.data.sizeYUnit * this.ctx.data.world.unitSize;\r\n        }\r\n        if (this.data.sizeZUnit >= 0) {\r\n            this.sizeZ = this.data.sizeZUnit * this.ctx.data.world.unitSize;\r\n        }\r\n\r\n        // 创建模型\r\n        if (this.data.modelCreator) {\r\n            if (this.model == null) {\r\n                if (this.modelCreatorPart == null) {\r\n                    this.modelCreatorPart = new _part_ModelCreatorPart_js__WEBPACK_IMPORTED_MODULE_1__.ModelCreatorPart(this.ctx, {\r\n                        parent: self,\r\n                    });\r\n                }\r\n                this.model = this.modelCreatorPart.build({\r\n                    parent: this,\r\n                    ...this.data.modelCreator,\r\n                    sizeX: this.sizeX,\r\n                    sizeY: this.sizeY,\r\n                    sizeZ: this.sizeZ,\r\n                });\r\n            }\r\n        }\r\n        // 加载外部模型\r\n        if (this.data.modelLoader) {\r\n            if (this.model == null) {\r\n                const loader = this.data.modelLoader;\r\n                const importResult =\r\n                    await (0,_util_import_fbx_model_util_js__WEBPACK_IMPORTED_MODULE_0__.importFBXModel)(this.ctx, loader.url, {\r\n                        debug: loader.debug || this.data.debug || this.ctx.debug,\r\n                        baseFileName: this.data.baseFileName,\r\n                    });\r\n                const model = importResult.model;\r\n                this.model = model;\r\n                this.ctx.scene.add(model);\r\n            }\r\n\r\n            // 计算模型边界盒\r\n            const box = this.getModelBox();\r\n            const size = box.getSize(new THREE.Vector3());\r\n\r\n            // // 计算缩放\r\n            // if (this.sizeY > 0) {\r\n            //     const scale = this.sizeY / size.y;\r\n            //     this.data.scale = scale;\r\n            //     this.model.scale.setScalar(scale);\r\n            // }\r\n        }\r\n        // 可碰撞对象\r\n        if (this.data.collidableObject) {\r\n        }\r\n        // 构建物理体\r\n        if (this.data.physBody) {\r\n            if (this.physBody == null) {\r\n                if (this.physBodyCreatorPart == null) {\r\n                    this.physBodyCreatorPart = new _part_PhysBodyCreatorPart_js__WEBPACK_IMPORTED_MODULE_2__.PhysBodyCreatorPart(this.ctx, {});\r\n                }\r\n                this.physBody = this.physBodyCreatorPart.build({\r\n                    debug: this.debug(),\r\n                    scale: this.data.scale,\r\n                    box: this.getModelBox(),\r\n                    ...this.data.physBody\r\n                });\r\n                this.togetherItemsMap['physBody'] = { core: this.physBody, pos: [0, 0, 0] };\r\n            }\r\n        }\r\n        // 助手\r\n        if (this.data.helper) {\r\n            if (this.helperCreatorPart == null) {\r\n                this.helperCreatorPart = new _part_HelperCreatorPart_js__WEBPACK_IMPORTED_MODULE_3__.HelperCreatorPart(this.ctx, {});\r\n            }\r\n            this.helperCreatorPart.build({\r\n                ...this.data.helper\r\n            })\r\n        }\r\n\r\n        if (this.model) {\r\n            if (this.model.userData == null) this.model.userData = {};\r\n            this.model.userData.cgo = this;\r\n        }\r\n        if (this.moreModels) {\r\n            for (const moreModel of this.moreModels) {\r\n                if (moreModel.userData == null) moreModel.userData = {};\r\n                moreModel.userData.cgo = this;\r\n            }\r\n        }\r\n\r\n        // 恢复选中模式\r\n        if (this.ctx.enableDevViewport) {\r\n            if (this.data.isSelected) {\r\n                this.activeSelectionMode();\r\n                // if (this.getModel()) {\r\n                //     const boxHelper = this.getModelBoxHelper();\r\n                //     boxHelper.update();\r\n                // }\r\n            }\r\n        }\r\n\r\n        // 旋转\r\n        if (this.data.rotationX != null) {\r\n            this.model.rotation.x = this.data.rotationX * Math.PI / 180;\r\n        }\r\n        if (this.data.rotationY != null) {\r\n            this.model.rotation.y = this.data.rotationY * Math.PI / 180;\r\n        }\r\n        if (this.data.rotationZ != null) {\r\n            this.model.rotation.z = this.data.rotationZ * Math.PI / 180;\r\n        }\r\n\r\n        // 缩放\r\n        if (this.data.scale != null && this.model) {\r\n            this.model.scale.setScalar(this.data.scale);\r\n        }\r\n\r\n        // 定位\r\n        if (this.data.posUnit) {\r\n            this.setPosUnit(this.data.posUnit[0], this.data.posUnit[1], this.data.posUnit[2]);\r\n        }\r\n        else if (this.data.pos) {\r\n            this.setPos(this.data.pos);\r\n        }\r\n        else {\r\n            this.setPos(0, 0, 0);\r\n        }\r\n\r\n        if (this.buildCount === 0) {\r\n            this.onFirstBuildEnd();\r\n        }\r\n        this.buildCount++;\r\n    }\r\n\r\n    update() {\r\n        this.updateModelBoxHelper();\r\n    }\r\n\r\n    debug() {\r\n        return this.debug || this.ctx.debug;\r\n    }\r\n\r\n    getPosUnit() {\r\n        return this.ctx.utils.getUnitSizeList(this.data.pos);\r\n    }\r\n\r\n    setPosUnit(x, y, z) {\r\n        if (x instanceof Array) {\r\n            this.setPos(this.getRealSizeList(x));\r\n        }\r\n        if (x instanceof Object) {\r\n            const t = x;\r\n            x = t[0];\r\n            y = t[1];\r\n            z = t[2];\r\n            this.setPos(this.ctx.utils.getRealSize(x), this.ctx.utils.getRealSize(y), this.ctx.utils.getRealSize(z));\r\n        }\r\n        else {\r\n            this.setPos(this.ctx.utils.getRealSize(x), this.ctx.utils.getRealSize(y), this.ctx.utils.getRealSize(z));\r\n        }\r\n    }\r\n\r\n    getPos() {\r\n        return this.data.pos;\r\n    }\r\n\r\n    setPos(x, y, z, opts = {}) {\r\n\r\n        if (x instanceof Array) {\r\n            const t = x;\r\n            opts = y ?? {};\r\n\r\n            x = t[0];\r\n            y = t[1];\r\n            z = t[2];\r\n        }\r\n        else if (x instanceof Object) {\r\n            const t = x;\r\n            opts = y ?? {};\r\n\r\n            x = t.x;\r\n            y = t.y;\r\n            z = t.z;\r\n        }\r\n\r\n        this.data.pos = [x, y, z];\r\n        this.data.posUnit = [this.ctx.utils.getUnitSize(x), this.ctx.utils.getUnitSize(y), this.ctx.utils.getUnitSize(z)];\r\n\r\n        if (this.model) this.model.position.set(x, y, z);\r\n\r\n        for (const prop in this.togetherItemsMap) {\r\n            const togetherItem = this.togetherItemsMap[prop];\r\n            togetherItem.core.position.set(x + togetherItem.pos[0], y + togetherItem.pos[1], z + togetherItem.pos[2]);\r\n        }\r\n\r\n        this.update();\r\n\r\n        if (opts.touchEvent !== false) {\r\n            this.onPosChange();\r\n        }\r\n    }\r\n\r\n    getData() {\r\n        return this.data;\r\n    }\r\n\r\n    getTags() {\r\n        return this.data.tags;\r\n    }\r\n\r\n    getModel() {\r\n        return this.model;\r\n    }\r\n\r\n    getMoreModels() {\r\n        return this.moreModels;\r\n    }\r\n\r\n    getModelBox() {\r\n        const { THREE } = this.ctx.imports;\r\n        if (this.modelBox == null) {\r\n            this.modelBox = new THREE.Box3().setFromObject(this.getModel());\r\n        }\r\n        return this.modelBox;\r\n    }\r\n\r\n    getModelBoxHelper() {\r\n        const { THREE } = this.ctx.imports;\r\n        const model = this.getModel();\r\n        if (model && this.modelBoxHelper == null) {\r\n            this.modelBoxHelper = new THREE.BoxHelper(model, 0x00ff00);\r\n            this.ctx.scene.add(this.modelBoxHelper);\r\n        }\r\n        return this.modelBoxHelper;\r\n    }\r\n\r\n    updateModelBoxHelper() {\r\n        if (this.modelBoxHelper) {\r\n            this.modelBoxHelper.update();\r\n        }\r\n    }\r\n\r\n    destroyModelBoxHelper() {\r\n        if (this.modelBoxHelper) {\r\n            this.ctx.scene.remove(this.modelBoxHelper);\r\n            this.modelBoxHelper = null;\r\n        }\r\n    }\r\n\r\n    getPhysBody() {\r\n        return this.physBody;\r\n    }\r\n\r\n    isSelectionModeActive() {\r\n        return this.data.isSelected;\r\n    }\r\n\r\n    /**\r\n     * 激活选中模式\r\n     */\r\n    activeSelectionMode() {\r\n        if (this.getModel()) {\r\n            const boxHelper = this.getModelBoxHelper();\r\n            this.data.isSelected = true;\r\n            this.activeSelectionModeTime = Date.now();\r\n\r\n            // const axesHelper = tjs_createAxesIndicator(this.ctx);\r\n            // this.model.add(axesHelper);\r\n\r\n            console.log('激活选中模式：', this);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 取消选中模式\r\n     */\r\n    deactiveSelectionMode() {\r\n        this.data.isSelected = false;\r\n        this.destroyModelBoxHelper();\r\n\r\n        // tjs_destroyAxesIndicator(this.model.getObjectByName('axesHelper'));\r\n    }\r\n\r\n    destroy() {\r\n        // 删除可见模型\r\n        this._destroyModel(this.model);\r\n        this.model = null;\r\n\r\n        // 删除更多模型\r\n        if (this.moreModels) {\r\n            for (const moreModel of this.moreModels) {\r\n                this._destroyModel(moreModel);\r\n            }\r\n            this.moreModels = [];\r\n        }\r\n\r\n        // 删除物理体\r\n        if (this.physBody) {\r\n            this.ctx.physWorld.removeBody(this.physBody);\r\n            this.physBody = null;\r\n        }\r\n\r\n        // 清空模型边界盒\r\n        if (this.modelBox) {\r\n            this.modelBox = null;\r\n        }\r\n        this.destroyModelBoxHelper();\r\n\r\n        // 清空联动对象\r\n        for (const prop in this.togetherItemsMap) {\r\n            delete this.togetherItemsMap[prop];\r\n        }\r\n    }\r\n\r\n    _destroyModel(model) {\r\n        // 删除可见模型\r\n        if (model.geometry) {\r\n            model.geometry.dispose();\r\n        }\r\n        if (model.material) {\r\n            if (Array.isArray(model.material)) {\r\n                model.material.forEach(m => {\r\n                    if (m.map) m.map.dispose();\r\n                    m.dispose();\r\n                });\r\n            } else {\r\n                if (model.material.map) model.material.map.dispose();\r\n                model.material.dispose();\r\n            }\r\n        }\r\n        this.ctx.scene.remove(model);\r\n    }\r\n\r\n    getPropValue(name) {\r\n        if (name === 'posXUnit') {\r\n            return this.data.posUnit[0];\r\n        }\r\n        else if (name === 'posYUnit') {\r\n            return this.data.posUnit[1];\r\n        }\r\n        else if (name === 'posZUnit') {\r\n            return this.data.posUnit[2];\r\n        }\r\n        return this.data[name];\r\n    }\r\n\r\n    async setPropValue(name, value) {\r\n        const list = this.constructor.getProps();\r\n        const prop = list.find(item => item.name == name);\r\n        if (prop) {\r\n            if (name === 'posXUnit') {\r\n                this.data.posUnit[0] = value;\r\n            }\r\n            else if (name === 'posYUnit') {\r\n                this.data.posUnit[1] = value;\r\n            }\r\n            else if (name === 'posZUnit') {\r\n                this.data.posUnit[2] = value;\r\n            }\r\n            else {\r\n                this.data[name] = value;\r\n            }\r\n\r\n            await this.build();\r\n            return true;\r\n        }\r\n        else {\r\n            return await super.setPropValue(name, value);\r\n        }\r\n    }\r\n\r\n    static getProps(opts = {}) {\r\n        const list = [];\r\n        list.push(...[\r\n            { label: '旋转X（度）', name: 'rotationX', type: 'Number', default: 0, step: 15 },\r\n            { label: '旋转Y（度）', name: 'rotationY', type: 'Number', default: 0, step: 15 },\r\n            { label: '旋转Z（度）', name: 'rotationZ', type: 'Number', default: 0, step: 15 },\r\n            { label: '缩放（单位）', name: 'scale', type: 'Number', default: 1, step: 0.5 },\r\n            { label: '坐标X（米）', name: 'posXUnit', type: 'Number', default: 0, step: 0.1 },\r\n            { label: '坐标Y（米）', name: 'posYUnit', type: 'Number', default: 0, step: 0.1 },\r\n            { label: '坐标Z（米）', name: 'posZUnit', type: 'Number', default: 0, step: 0.1 },\r\n        ]);\r\n        if (opts.onlySelf !== true) {\r\n            list.push(...super.getProps());\r\n        }\r\n        return list;\r\n    }\r\n\r\n    onFirstBuildEnd() {\r\n\r\n    }\r\n\r\n    onPosChange() {\r\n\r\n    }\r\n}\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/model/CGObjectModel.js?\n}");

/***/ }),

/***/ "./src/model/CGPaintingFrameModel.js":
/*!*******************************************!*\
  !*** ./src/model/CGPaintingFrameModel.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CGPaintingFrameModel: () => (/* binding */ CGPaintingFrameModel)\n/* harmony export */ });\n/* harmony import */ var _util_tjs_geometry_util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/tjs-geometry-util */ \"./src/util/tjs-geometry-util.js\");\n/* harmony import */ var _CGObjectModel__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CGObjectModel */ \"./src/model/CGObjectModel.js\");\n\r\n\r\n/**\r\n * 画框\r\n */\r\nclass CGPaintingFrameModel extends _CGObjectModel__WEBPACK_IMPORTED_MODULE_1__.CGObjectModel {\r\n    constructor(ctx, data) {\r\n        super(ctx, data);\r\n\r\n        data.modelLoader = {\r\n            url: `./data/import-model/frame-0`,\r\n        };\r\n    }\r\n\r\n    async build() {\r\n        await super.build();\r\n\r\n        const imagePlaneWidth = 95;\r\n        const imagePlaneHeight = 125;\r\n        const imagePlane = (0,_util_tjs_geometry_util__WEBPACK_IMPORTED_MODULE_0__.tjs_createPlane)(this.ctx, imagePlaneWidth, imagePlaneHeight, {\r\n            side: 'front', alignmentPointType: 'bottom',\r\n            imageSource: `./data/resource/${this.ctx.data.project.id}/img/${this.data.imageSource}`\r\n        });\r\n        imagePlane.position.y = 27;\r\n\r\n        this.model.add(imagePlane);\r\n    }\r\n\r\n    static getProps(opts = {}) {\r\n        const list = [];\r\n        list.push(...[\r\n            { label: '图片源', name: 'imageSource', type: 'String' },\r\n        ]);\r\n        if (opts.onlySelf !== true) {\r\n            list.push(...super.getProps());\r\n        }\r\n        return list;\r\n    }\r\n}\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/model/CGPaintingFrameModel.js?\n}");

/***/ }),

/***/ "./src/model/CGPlayerStartModel.js":
/*!*****************************************!*\
  !*** ./src/model/CGPlayerStartModel.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CGPlayerStartModel: () => (/* binding */ CGPlayerStartModel)\n/* harmony export */ });\n/* harmony import */ var _util_tjs_geometry_util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/tjs-geometry-util */ \"./src/util/tjs-geometry-util.js\");\n/* harmony import */ var _CGObjectModel__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CGObjectModel */ \"./src/model/CGObjectModel.js\");\n\r\n\r\n/**\r\n * 玩家起点\r\n * 用于给设计师定位玩家开始游戏时的出生点\r\n */\r\nclass CGPlayerStartModel extends _CGObjectModel__WEBPACK_IMPORTED_MODULE_1__.CGObjectModel {\r\n    constructor(ctx, data) {\r\n        super(ctx, data);\r\n    }\r\n\r\n    async build() {\r\n\r\n        if (this.model == null) {\r\n            const opacity = 0.8;\r\n            let headRadius = 0.3;\r\n            let heightUnit = this.ctx.data.player.heightUnit;\r\n            heightUnit = 1;\r\n\r\n            // *** 创建身体 ***\r\n            let sizeY = this.getRealSize(heightUnit);\r\n            this.model = (0,_util_tjs_geometry_util__WEBPACK_IMPORTED_MODULE_0__.tjs_createCylinder)(this.ctx, this.getRealSize(0.2), this.getRealSize(0.5), sizeY, {\r\n                opacity\r\n            });\r\n\r\n            // 对齐点设为底部\r\n            this.model.geometry.translate(0, (sizeY / 2), 0);\r\n            this.ctx.scene.add(this.model);\r\n            // this.model.rotation.x = Math.PI * 0.5;\r\n\r\n            // *** 创建头部 ***\r\n            const sphere = (0,_util_tjs_geometry_util__WEBPACK_IMPORTED_MODULE_0__.tjs_createSphere)(this.ctx, this.getRealSize(headRadius), {\r\n                opacity\r\n            });\r\n            sphere.position.y = this.getRealSize(heightUnit + headRadius + 0.1);\r\n            this.model.add(sphere);\r\n\r\n            // *** 创建朝向指示器 ***\r\n            const directionIndicator = this.createDirectionIndicator({\r\n                headRadius, heightUnit\r\n            });\r\n            this.model.add(directionIndicator);\r\n        }\r\n\r\n        await super.build();\r\n    }\r\n\r\n    /**\r\n     * 创建朝向指示器\r\n     */\r\n    createDirectionIndicator(pars) {\r\n\r\n        let fromPos = [0, this.getRealSize(pars.headRadius + pars.heightUnit + 0.1), 0];\r\n        let toPos = [0, 0, 0];\r\n\r\n        if (this.data.lookAtPosUnitX != null) {\r\n            toPos[0] = this.getRealSize(this.data.lookAtPosUnitX);\r\n        }\r\n        else {\r\n            toPos[0] = this.getRealSize(this.ctx.data.player.startLookAtPosUnit[0]);\r\n        }\r\n\r\n        if (this.data.lookAtPosUnitY != null) {\r\n            toPos[1] = this.getRealSize(pars.headRadius + pars.heightUnit + 0.1) + this.getRealSize(this.data.lookAtPosUnitY);\r\n        }\r\n        else {\r\n            toPos[1] = this.getRealSize(pars.headRadius + pars.heightUnit + 0.1) + this.getRealSize(this.ctx.data.player.startLookAtPosUnit[1]);\r\n        }\r\n\r\n        if (this.data.lookAtPosUnitZ != null) {\r\n            toPos[2] = this.getRealSize(this.data.lookAtPosUnitZ);\r\n        }\r\n        else {\r\n            toPos[2] = this.getRealSize(this.ctx.data.player.startLookAtPosUnit[2]);\r\n        }\r\n\r\n        const indicator = (0,_util_tjs_geometry_util__WEBPACK_IMPORTED_MODULE_0__.tjs_createCylinderByTwoPoint)(this.ctx, 1, 3, toPos, fromPos);\r\n        return indicator;\r\n    }\r\n\r\n    onFirstBuildEnd() {\r\n        this.data.lookAtPosUnitX = this.ctx.data.player.startLookAtPosUnit[0];\r\n        this.data.lookAtPosUnitY = this.ctx.data.player.startLookAtPosUnit[1];\r\n        this.data.lookAtPosUnitZ = this.ctx.data.player.startLookAtPosUnit[2];\r\n\r\n        this.setPosUnit(this.ctx.data.player.startPosUnit, { touchEvent: false });\r\n    }\r\n\r\n    onPosChange() {\r\n        this.ctx.data.player.startPosUnit = this.getPosUnit();\r\n    }\r\n\r\n    async setPropValue(name, value) {\r\n        const list = this.constructor.getProps({ onlySelf: true });\r\n        const prop = list.find(item => item.name == name);\r\n        if (prop) {\r\n            this.data[name] = value;\r\n\r\n            if (name === 'lookAtPosUnitX') {\r\n                this.ctx.data.player.startLookAtPosUnit[0] = value;\r\n            }\r\n            else if (name === 'lookAtPosUnitY') {\r\n                this.ctx.data.player.startLookAtPosUnit[1] = value;\r\n            }\r\n            else if (name === 'lookAtPosUnitZ') {\r\n                this.ctx.data.player.startLookAtPosUnit[2] = value;\r\n            }\r\n\r\n            this.destroy();\r\n            await this.build();\r\n            return true;\r\n        }\r\n        else {\r\n            return super.setPropValue(name, value);\r\n        }\r\n    }\r\n\r\n    static getProps(opts = {}) {\r\n        const list = [];\r\n        list.push(...[\r\n            { label: '朝向点X', name: 'lookAtPosUnitX', type: 'Number', default: 0, step: 1 },\r\n            { label: '朝向点Y', name: 'lookAtPosUnitY', type: 'Number', default: 0, step: 1 },\r\n            { label: '朝向点Z', name: 'lookAtPosUnitZ', type: 'Number', default: 0, step: 1 },\r\n        ]);\r\n        // if (opts.onlySelf !== true) {\r\n        //     list.push(...super.getProps());\r\n        // }\r\n        return list;\r\n    }\r\n}\r\n\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/model/CGPlayerStartModel.js?\n}");

/***/ }),

/***/ "./src/model/CGSkyModel.js":
/*!*********************************!*\
  !*** ./src/model/CGSkyModel.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CGSkyModel: () => (/* binding */ CGSkyModel)\n/* harmony export */ });\n/* harmony import */ var _CGObjectModel_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./CGObjectModel.js */ \"./src/model/CGObjectModel.js\");\n\r\n/**\r\n * 天空模型\r\n */\r\nclass CGSkyModel extends _CGObjectModel_js__WEBPACK_IMPORTED_MODULE_0__.CGObjectModel {\r\n    constructor(ctx, data) {\r\n        super(ctx, data);\r\n\r\n        const baseData = {\r\n            turbidity: 5,          // 大气浑浊度，控制天空的雾霾程度\r\n            rayleigh: 1,            // 瑞利散射系数，影响天空的蓝色程度\r\n            mieCoefficient: 0.05,  // 米氏散射系数，影响大气中颗粒物的散射\r\n            mieDirectionalG: 0.99999,   // 米氏散射方向性参数，控制散射的方向性\r\n            elevation: 50,           // 太阳仰角（度），控制太阳在天空中的高度\r\n            azimuth: 180,           // 太阳方位角（度），控制太阳的方向\r\n            exposure: ctx.renderer.toneMappingExposure,  // 曝光度，控制整体亮度\r\n        };\r\n\r\n        for (const prop in baseData) {\r\n            if (data[prop] == null) {\r\n                data[prop] = baseData[prop];\r\n            }\r\n        }\r\n    }\r\n\r\n    build() {\r\n        const { THREE, Sky } = this.ctx.imports;\r\n\r\n        // 天空\r\n        if (this.sky == null) {\r\n            const sky = new Sky();\r\n            sky.scale.setScalar(450000);\r\n            this.ctx.scene.add(sky);\r\n            this.sky = sky;\r\n        }\r\n        this.sky.material.uniforms.turbidity.value = this.data.turbidity;\r\n        this.sky.material.uniforms.rayleigh.value = this.data.rayleigh;\r\n        this.sky.material.uniforms.mieCoefficient.value = this.data.mieCoefficient;\r\n        this.sky.material.uniforms.mieDirectionalG.value = this.data.mieDirectionalG;\r\n\r\n\r\n        // 太阳\r\n        if (this.sun == null) {\r\n            const sun = new THREE.Vector3();\r\n            // sun.setFromSphericalCoords(1, data.elevation * Math.PI / 180, data.azimuth * Math.PI / 180);\r\n            this.sun = sun;\r\n        }\r\n\r\n        // 太阳位置\r\n        const phi = THREE.MathUtils.degToRad(90 - this.data.elevation);\r\n        const theta = THREE.MathUtils.degToRad(this.data.azimuth);\r\n        this.sun.setFromSphericalCoords(1, phi, theta);\r\n        this.sky.material.uniforms.sunPosition.value.copy(this.sun);\r\n\r\n        // 添加太阳光源（方向光）\r\n        if (this.sunLight == null) {\r\n            // // 太阳高度\r\n            // let sunHeight = 1000;\r\n            // // 太阳距离场景中心的水平距离\r\n            // let sunDistance = 2000;\r\n\r\n            // *** 点光源 ***\r\n            this.sunLight = new THREE.PointLight(0xffffff, 5, 0, 0.2);\r\n            this.sunLight.castShadow = true;\r\n            // 设置阴影精度\r\n            this.sunLight.shadow.mapSize.width = 1024 * 2;\r\n            this.sunLight.shadow.mapSize.height = 1024 * 2;\r\n            // 设置阴影范围（关键）\r\n            this.sunLight.shadow.camera.near = 0.5;\r\n            this.sunLight.shadow.camera.far = 100000;\r\n            this.ctx.scene.add(this.sunLight);\r\n\r\n            if (this.ctx.enableDevViewport) {\r\n                // 添加点光源辅助对象\r\n                const pointLightHelper = new THREE.PointLightHelper(this.sunLight, 5);\r\n                this.ctx.scene.add(pointLightHelper);\r\n\r\n                const helper = new THREE.CameraHelper(this.sunLight.shadow.camera);\r\n                this.ctx.scene.add(helper);\r\n            }\r\n        }\r\n        \r\n        // 根据太阳的elevation和azimuth动态设置光源位置\r\n        const sunDistance = 5000;\r\n        const elevationRad = THREE.MathUtils.degToRad(this.data.elevation);\r\n        const azimuthRad = THREE.MathUtils.degToRad(this.data.azimuth);\r\n        \r\n        // 使用球坐标计算光源位置\r\n        this.sunLight.position.x = sunDistance * Math.cos(elevationRad) * Math.sin(azimuthRad);\r\n        this.sunLight.position.y = sunDistance * Math.sin(elevationRad);\r\n        this.sunLight.position.z = sunDistance * Math.cos(elevationRad) * Math.cos(azimuthRad);\r\n        \r\n        // 确保光源不会在地面以下\r\n        if (this.sunLight.position.y < 100) {\r\n            this.sunLight.position.y = 100;\r\n        }\r\n\r\n        this.ctx.renderer.toneMappingExposure = this.data.exposure;\r\n\r\n        this.model = this.sky;\r\n    }\r\n}\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/model/CGSkyModel.js?\n}");

/***/ }),

/***/ "./src/model/CGWallModel.js":
/*!**********************************!*\
  !*** ./src/model/CGWallModel.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CGWallModel: () => (/* binding */ CGWallModel)\n/* harmony export */ });\n/* harmony import */ var _CGObjectModel__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./CGObjectModel */ \"./src/model/CGObjectModel.js\");\n\r\n/**\r\n * 墙体模型\r\n */\r\nclass CGWallModel extends _CGObjectModel__WEBPACK_IMPORTED_MODULE_0__.CGObjectModel {\r\n    constructor(ctx, data) {\r\n        super(ctx, data);\r\n\r\n        // 长度\r\n        if (data.sizeXUnit == null) data.sizeXUnit = 10;\r\n        // 高度\r\n        if (data.sizeYUnit == null) data.sizeYUnit = 6;\r\n        // 厚度\r\n        if (data.sizeZUnit == null) data.sizeZUnit = 0.2;\r\n        // 对齐点设为底部\r\n        data.alignmentPointType = 'bottom';\r\n\r\n        data.modelCreator = {\r\n            type: 'cube',\r\n        };\r\n\r\n        data.collidableObject = {};\r\n    }\r\n\r\n    async build() {\r\n        await super.build();\r\n    }\r\n\r\n    async setPropValue(name, value) {\r\n        const list = this.constructor.getProps({ onlySelf: true });\r\n        const prop = list.find(item => item.name == name);\r\n        if (prop) {\r\n            this.data[name] = value;\r\n            this.destroy();\r\n            await this.build();\r\n            return true;\r\n        }\r\n        else {\r\n            return await super.setPropValue(name, value);\r\n        }\r\n    }\r\n\r\n    static getProps(opts = {}) {\r\n        const list = [];\r\n        list.push(...[\r\n            { label: '长度（米）', name: 'sizeXUnit', type: 'Number' },\r\n            { label: '高度（米）', name: 'sizeYUnit', type: 'Number' },\r\n            { label: '厚度（米）', name: 'sizeZUnit', type: 'Number' },\r\n        ]);\r\n        if (opts.onlySelf !== true) {\r\n            list.push(...super.getProps());\r\n        }\r\n        return list;\r\n    }\r\n}\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/model/CGWallModel.js?\n}");

/***/ }),

/***/ "./src/model/CGWallMountedTelevisionModel.js":
/*!***************************************************!*\
  !*** ./src/model/CGWallMountedTelevisionModel.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CGWallMountedTelevisionModel: () => (/* binding */ CGWallMountedTelevisionModel)\n/* harmony export */ });\n/* harmony import */ var _util_tjs_geometry_util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/tjs-geometry-util */ \"./src/util/tjs-geometry-util.js\");\n/* harmony import */ var _CGObjectModel__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CGObjectModel */ \"./src/model/CGObjectModel.js\");\n\r\n\r\n/**\r\n * 壁挂电视模型\r\n */\r\nclass CGWallMountedTelevisionModel extends _CGObjectModel__WEBPACK_IMPORTED_MODULE_1__.CGObjectModel {\r\n    constructor(ctx, data) {\r\n        super(ctx, data);\r\n\r\n        if (data.videoSource == null) data.videoSource = '';\r\n        // 长度\r\n        if (data.sizeXUnit == null) data.sizeXUnit = 10;\r\n        // 高度\r\n        if (data.sizeYUnit == null) data.sizeYUnit = 6;\r\n        // 厚度\r\n        if (data.sizeZUnit == null) data.sizeZUnit = 0.1;\r\n\r\n        this._hasCreateAudio = false;\r\n    }\r\n\r\n    async build() {\r\n        if (this.model == null) {\r\n            let url = this.getVideoUrl();\r\n\r\n            // if (this.data.videoSource) {\r\n            //     if (this.data.videoSource.startsWith('http')) {\r\n            //         url = this.data.videoSource;\r\n            //     }\r\n            //     else {\r\n            //         url = `./data/resource/${this.ctx.data.project.id}/video/${this.data.videoSource}`;\r\n            //     }\r\n            // }\r\n\r\n\r\n            this.model = (0,_util_tjs_geometry_util__WEBPACK_IMPORTED_MODULE_0__.tjs_createPlane)(this.ctx, this.getRealSize(this.data.sizeXUnit), this.getRealSize(this.data.sizeYUnit), {\r\n                videoSource: url,\r\n                alignmentPointType: 'bottom',\r\n                side: 'front',\r\n            });\r\n\r\n\r\n            const back = (0,_util_tjs_geometry_util__WEBPACK_IMPORTED_MODULE_0__.tjs_createPlane)(this.ctx, this.getRealSize(this.data.sizeXUnit), this.getRealSize(this.data.sizeYUnit), {\r\n                side: 'back',\r\n                alignmentPointType: 'bottom',\r\n            });\r\n            this.model.add(back);\r\n\r\n            this.ctx.scene.add(this.model);\r\n        }\r\n        await super.build();\r\n    }\r\n\r\n    getVideoUrl() {\r\n        if (this.data.videoSource) {\r\n            if (this.data.videoSource.startsWith('http')) {\r\n                return this.data.videoSource;\r\n            }\r\n            else {\r\n                return `./data/resource/${this.ctx.data.project.id}/video/${this.data.videoSource}`;\r\n            }\r\n        }\r\n    }\r\n\r\n    destroy() {\r\n        if (this.model) {\r\n            if (self.model.userData.sound) {\r\n                self.model.userData.sound.disconnect();\r\n            }\r\n        }\r\n\r\n        super.destroy();\r\n    }\r\n\r\n    async setPropValue(name, value) {\r\n        const list = this.constructor.getProps({ onlySelf: true });\r\n        const prop = list.find(item => item.name == name);\r\n        if (prop) {\r\n            this.data[name] = value;\r\n            this.destroy();\r\n            await this.build();\r\n            return true;\r\n        }\r\n        else {\r\n            return await super.setPropValue(name, value);\r\n        }\r\n    }\r\n\r\n    static getProps(opts = {}) {\r\n        const list = [];\r\n        list.push(...[\r\n            { label: '视频源', name: 'videoSource', type: 'String' },\r\n            { label: '长度（米）', name: 'sizeXUnit', type: 'Number' },\r\n            { label: '高度（米）', name: 'sizeYUnit', type: 'Number' },\r\n            { label: '厚度（米）', name: 'sizeZUnit', type: 'Number' },\r\n        ]);\r\n        if (opts.onlySelf !== true) {\r\n            list.push(...super.getProps());\r\n        }\r\n        return list;\r\n    }\r\n\r\n    onFirstBuildEnd() {\r\n        const self = this;\r\n        const userControlsAbility = this.ctx.cgWorld.getAbility('user_ctrl');\r\n        userControlsAbility.setUserEvent(`wall_mounted_television_${self.getId()}`,\r\n            [\"mouseleftdown\", \"touchstart\"], (e, type) => {\r\n                const video = self.model.userData.video;\r\n                let sound = self.model.userData.sound;\r\n\r\n                if (video) {\r\n                    video.play();\r\n\r\n                    if (!self._hasCreateAudio) {\r\n                        self._hasCreateAudio = true;\r\n                        const { THREE } = self.ctx.imports;\r\n\r\n                        // 音频监听器\r\n                        const listener = new THREE.AudioListener();\r\n                        self.ctx.camera.add(listener);\r\n\r\n                        // 声音源（方法1）\r\n                        // sound = new THREE.PositionalAudio(listener);\r\n                        // // 加载视频音轨作为音频源\r\n                        // // const mediaElementSource = listener.context.createMediaElementSource(video);\r\n                        // // sound.setNodeSource(mediaElementSource);\r\n                        // sound.setMediaElementSource(video);\r\n                        // sound.setRefDistance(1);   // 声音在 1 米时音量最大\r\n                        // sound.setRolloffFactor(1); // 衰减速度（可以调大调小）\r\n                        // sound.setDistanceModel('linear'); // 线性衰减\r\n                        // // sound.setVolume(1); // 设置初始音量\r\n                        // self.model.add(sound); // model是Mesh\r\n\r\n                        // 声音源（方法2-可行）\r\n                        sound = new THREE.PositionalAudio(listener);\r\n                        self.model.add(sound); // model是Mesh\r\n\r\n                        let audioLoader = new THREE.AudioLoader();\r\n                        // 加载音频文件，返回一个音频缓冲区对象作为回调函数参数\r\n                        audioLoader.load(video.src, function (AudioBuffer) {\r\n                            // console.log(buffer);\r\n                            // 音频缓冲区对象关联到音频对象audio\r\n                            sound.setBuffer(AudioBuffer);\r\n                            sound.setVolume(3); //音量\r\n                            sound.setLoop(true);\r\n                            sound.setRefDistance(50); //参数值越大,声音越大\r\n                            sound.setRolloffFactor(1); // 衰减速度（可以调大调小）\r\n                            sound.play(); //播放\r\n                        });\r\n                    }\r\n                }\r\n            });\r\n    }\r\n}\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/model/CGWallMountedTelevisionModel.js?\n}");

/***/ }),

/***/ "./src/model/CGWorldModel.js":
/*!***********************************!*\
  !*** ./src/model/CGWorldModel.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CGWorldModel: () => (/* binding */ CGWorldModel)\n/* harmony export */ });\n/* harmony import */ var _util_id_util_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../util/id-util.js */ \"./src/util/id-util.js\");\n\r\n\r\n/**\r\n * CS Game World Model\r\n */\r\nclass CGWorldModel {\r\n    constructor(ctx, data = {}) {\r\n        this.ctx = ctx;\r\n        this.data = data;\r\n\r\n        // 所有游戏对象\r\n        this._cgoMap = {};\r\n        // 物理动态对象（mass大于0的）\r\n        this._physDynBodiesMap = {};\r\n\r\n        // 可碰撞物体\r\n        this._collidableObjectsMap = {};\r\n        // 可碰撞物体（缓存）\r\n        this._collidableObjectsCache;\r\n\r\n        // 能力字典集\r\n        this._abilities = {};\r\n    }\r\n\r\n    /**\r\n     * \r\n     * @returns {CGObjectModel[]}\r\n     */\r\n    getObjects() {\r\n        const list = [];\r\n        for (const id in this._cgoMap) {\r\n            list.push(this._cgoMap[id].core);\r\n        }\r\n        return list;\r\n    }\r\n\r\n    /**\r\n     * 获取所有可碰撞的对象\r\n     * @returns {THREE.Object3D[]}\r\n     */\r\n    getCollidableObjects() {\r\n        if (this._collidableObjectsCache == null) {\r\n            const list = [];\r\n            for (const id in this._collidableObjectsMap) {\r\n                const collidableObjectCore = this._collidableObjectsMap[id].core;\r\n                list.push(collidableObjectCore.getModel());\r\n                if (collidableObjectCore.getMoreModels() && collidableObjectCore.getMoreModels().length > 0) {\r\n                    list.push(...collidableObjectCore.getMoreModels());\r\n                }\r\n            }\r\n            this._collidableObjectsCache = list;\r\n        }\r\n        return this._collidableObjectsCache;\r\n    }\r\n\r\n    /**\r\n     * \r\n     * @param {*} pars \r\n     * @returns {CGObjectModel[]}\r\n     */\r\n    getSelectedObjects(pars = {}) {\r\n        const list = [];\r\n        for (const id in this._cgoMap) {\r\n            const cgo = this._cgoMap[id].core;\r\n            if (cgo.isSelectionModeActive()) {\r\n                if (pars.excludeIds && pars.excludeIds.includes(cgo.getId())) {\r\n                    continue;\r\n                }\r\n                list.push(cgo);\r\n            }\r\n        }\r\n        return list;\r\n    }\r\n\r\n    /**\r\n     * \r\n     * @returns {CGObjectModel}\r\n     */\r\n    getSelectedObject() {\r\n        const list = this.getSelectedObjects();\r\n        if (list.length > 1) {\r\n            list.sort(function (b, a) {\r\n                if (a.activeSelectionModeTime > b.activeSelectionModeTime) {\r\n                    return 1;\r\n                }\r\n                else if (a.activeSelectionModeTime < b.activeSelectionModeTime) {\r\n                    return -1;\r\n                }\r\n                return 0;\r\n            });\r\n            return list[0];\r\n        }\r\n        if (list.length > 0) {\r\n            return list[0];\r\n        }\r\n        return null;\r\n    }\r\n\r\n    async buildObjects() {\r\n        for (const id in this._cgoMap) {\r\n            const cgo = this._cgoMap[id].core;\r\n            await cgo.build();\r\n        }\r\n    }\r\n\r\n    async addObjectAndBuild(obj) {\r\n        await this.addObject(obj);\r\n        await obj.build();\r\n    }\r\n\r\n    async addObject(obj) {\r\n        this._cgoMap[obj.getId()] = { core: obj };\r\n\r\n        if (obj.getData().collidableObject) {\r\n            this._collidableObjectsMap[obj.getId()] = { core: obj };\r\n            this._collidableObjectsCache = null;\r\n        }\r\n\r\n        if (obj.getData().physBody) {\r\n            if (obj.getData().physBody.mass > 0) {\r\n                this._physDynBodiesMap[obj.getId()] = { core: obj };\r\n            }\r\n        }\r\n\r\n        // await obj.build();\r\n        return obj;\r\n    }\r\n\r\n    async removeObject(obj) {\r\n        return await this.removeObjectById(obj.getId());\r\n    }\r\n\r\n    async removeObjectById(id) {\r\n        let objItem = this._cgoMap[id];\r\n        if (objItem) {\r\n            objItem.core.destroy();\r\n        }\r\n        delete this._cgoMap[id];\r\n        delete this._physDynBodiesMap[id];\r\n        delete this._collidableObjectsMap[id];\r\n        this._collidableObjectsCache = null;\r\n    }\r\n\r\n    async startAbilites() {\r\n        for (const name in this._abilities) {\r\n            const ability = this._abilities[name];\r\n            if (ability.start) {\r\n                ability.start({\r\n                    cgWorld: this\r\n                });\r\n            }\r\n        }\r\n    }\r\n\r\n    getAbility(name) {\r\n        return this._abilities[name];\r\n    }\r\n\r\n    async addAbility(obj, name, pars = {}) {\r\n        if (name == null) {\r\n            name = (0,_util_id_util_js__WEBPACK_IMPORTED_MODULE_0__.newCUIdA)();\r\n            console.log('ability name is null, auto create: ', name, obj);\r\n        }\r\n        this._abilities[name] = obj;\r\n        if (obj.init) obj.init(pars);\r\n    }\r\n\r\n    /**\r\n     * 刷新世界\r\n     * @param {*} pars \r\n     */\r\n    update(pars) {\r\n        this.updateObjects(pars);\r\n        this.updateAbilities(pars);\r\n        this.updatePhysDynBodies(pars);\r\n    }\r\n\r\n    updateObjects(pars) {\r\n        for (const id in this._cgoMap) {\r\n            const cgo = this._cgoMap[id].core;\r\n            if (cgo.update) {\r\n                cgo.update(pars);\r\n            }\r\n        }\r\n    }\r\n\r\n    updateAbilities(pars) {\r\n        for (const name in this._abilities) {\r\n            const ability = this._abilities[name];\r\n            if (ability.update) {\r\n                ability.update(pars);\r\n            }\r\n        }\r\n    }\r\n\r\n    updatePhysDynBodies(pars) {\r\n        for (const prop in this._physDynBodiesMap) {\r\n            const physDynBodyItem = this._physDynBodiesMap[prop];\r\n            // 同步物理体坐标\r\n            const { x, y, z } = physDynBodyItem.core.getPhysBody().position;\r\n            physDynBodyItem.core.setPos(x, y, z)\r\n            // physDynBodyItem.core.getModel().position.copy(physDynBodyItem.core.getPhysBody().position);\r\n            physDynBodyItem.core.getModel().quaternion.copy(physDynBodyItem.core.getPhysBody().quaternion);\r\n        }\r\n    }\r\n\r\n    resize(pars) {\r\n        this.resizeObjects(pars);\r\n    }\r\n\r\n    resizeObjects(pars) {\r\n        for (const id in this._cgoMap) {\r\n            const cgo = this._cgoMap[id].core;\r\n            if (cgo.resize) {\r\n                cgo.resize(pars);\r\n            }\r\n        }\r\n    }\r\n}\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/model/CGWorldModel.js?\n}");

/***/ }),

/***/ "./src/model/part/HelperCreatorPart.js":
/*!*********************************************!*\
  !*** ./src/model/part/HelperCreatorPart.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HelperCreatorPart: () => (/* binding */ HelperCreatorPart)\n/* harmony export */ });\nclass HelperCreatorPart {\r\n    constructor(ctx, data = {}) {\r\n        this.ctx = ctx;\r\n        this.data = data;\r\n    }\r\n\r\n    build(pars) {\r\n        const { CSS2DObject, THREE } = this.ctx.imports;\r\n        const { type } = pars;\r\n\r\n        // 中心坐标指示器\r\n        if (type === 'center-axes-indicator') {\r\n            this.cfg = {};\r\n            // 中心点坐标指示器尺寸\r\n            if (this.cfg.caiAxesSize == null) this.cfg.caiAxesSize = 200;\r\n            if (this.cfg.caiArrowRadius == null) this.cfg.caiArrowRadius = 1;\r\n\r\n            let arrowLabelDistance = 5;\r\n\r\n            const axesHelper = new THREE.AxesHelper(this.cfg.caiAxesSize);\r\n            axesHelper.renderOrder = 1;\r\n            axesHelper.position.set(0, 0, 0);\r\n\r\n            {\r\n                // x轴箭头（红色）\r\n                const geometry = new THREE.ConeGeometry(this.cfg.caiArrowRadius, this.cfg.caiAxesSize / 5, 32);\r\n                const material = new THREE.MeshBasicMaterial({ color: 'red' });\r\n                const cone = new THREE.Mesh(geometry, material);\r\n                cone.position.set(this.cfg.caiAxesSize, 0, 0);\r\n                cone.rotateZ(-Math.PI / 180 * 90);\r\n                axesHelper.add(cone);\r\n\r\n                // x轴标签\r\n                const labelDiv = document.createElement('div');\r\n                labelDiv.textContent = '+X';\r\n                labelDiv.style = 'color:silver;';\r\n                const css2dLabel = new CSS2DObject(labelDiv);\r\n                css2dLabel.position.set(this.cfg.caiAxesSize + arrowLabelDistance, 0, 0);\r\n                axesHelper.add(css2dLabel);\r\n            }\r\n            {\r\n                // y轴箭头（绿色）\r\n                const geometry = new THREE.ConeGeometry(this.cfg.caiArrowRadius, this.cfg.caiAxesSize / 5, 32);\r\n                const material = new THREE.MeshBasicMaterial({ color: 'green' });\r\n                const cone = new THREE.Mesh(geometry, material);\r\n                cone.position.set(0, this.cfg.caiAxesSize, 0);\r\n                axesHelper.add(cone);\r\n\r\n                // y轴标签\r\n                const labelDiv = document.createElement('div');\r\n                labelDiv.textContent = '+Y';\r\n                labelDiv.style = 'color:silver;';\r\n                const css2dLabel = new CSS2DObject(labelDiv);\r\n                css2dLabel.position.set(0, this.cfg.caiAxesSize + arrowLabelDistance, 0);\r\n                axesHelper.add(css2dLabel);\r\n            }\r\n            {\r\n                // z轴箭头（蓝色）\r\n                const geometry = new THREE.ConeGeometry(this.cfg.caiArrowRadius, this.cfg.caiAxesSize / 5, 32);\r\n                const material = new THREE.MeshBasicMaterial({ color: 'blue' });\r\n                const cone = new THREE.Mesh(geometry, material);\r\n                cone.position.set(0, 0, this.cfg.caiAxesSize);\r\n                cone.rotateX(Math.PI / 180 * 90);\r\n                axesHelper.add(cone);\r\n\r\n                // z轴标签\r\n                const labelDiv = document.createElement('div');\r\n                labelDiv.textContent = '+Z';\r\n                labelDiv.style = 'color:silver;';\r\n                const css2dLabel = new CSS2DObject(labelDiv);\r\n                css2dLabel.position.set(0, 0, this.cfg.caiAxesSize + arrowLabelDistance);\r\n                axesHelper.add(css2dLabel);\r\n            }\r\n\r\n            this.ctx.scene.add(axesHelper);\r\n        }\r\n    }\r\n}\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/model/part/HelperCreatorPart.js?\n}");

/***/ }),

/***/ "./src/model/part/ModelCreatorPart.js":
/*!********************************************!*\
  !*** ./src/model/part/ModelCreatorPart.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ModelCreatorPart: () => (/* binding */ ModelCreatorPart)\n/* harmony export */ });\n/* harmony import */ var _util_tjs_geometry_util__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../util/tjs-geometry-util */ \"./src/util/tjs-geometry-util.js\");\n\r\n\r\nclass ModelCreatorPart {\r\n    constructor(ctx, data) {\r\n        this.ctx = ctx;\r\n        this.data = data;\r\n    }\r\n\r\n    build(pars) {\r\n        const { THREE } = this.ctx.imports;\r\n\r\n        let type = pars.type;\r\n        let sizeX = pars.sizeX;\r\n        let sizeY = pars.sizeY;\r\n        let sizeZ = pars.sizeZ;\r\n\r\n        // 立方体\r\n        if (type === 'cube') {\r\n            const cubeMesh = (0,_util_tjs_geometry_util__WEBPACK_IMPORTED_MODULE_0__.tjs_createCube)(this.ctx, sizeX, sizeY, sizeZ, {\r\n                imageSource: pars.parent.data.imageSource\r\n            });\r\n\r\n            cubeMesh.castShadow = true;\r\n            cubeMesh.receiveShadow = true;\r\n            this.ctx.scene.add(cubeMesh);\r\n\r\n            // 对齐点设为底部\r\n            if (pars.parent.data.alignmentPointType === 'bottom') {\r\n                cubeMesh.geometry.translate(0, (sizeY / 2), 0);\r\n            }\r\n\r\n            return cubeMesh;\r\n        }\r\n        // 地面\r\n        else if (type === 'ground') {\r\n            const groundGeometry = new THREE.PlaneGeometry(sizeX, sizeZ);\r\n            const groundMaterial = new THREE.MeshStandardMaterial({\r\n                color: 0x808080,\r\n                // side: THREE.DoubleSidde, // 两面都显示\r\n            });\r\n            const ground = new THREE.Mesh(groundGeometry, groundMaterial);\r\n            ground.position.y = 0;\r\n            ground.receiveShadow = true;\r\n            this.ctx.scene.add(ground);\r\n\r\n            // *** 围绕边缘的墙 ***\r\n            if (pars.aroundBorderWalls) {\r\n                let wallHeightUnit = 2;\r\n                let farBorderUnit = 0;\r\n                const wallHeight = this.ctx.utils.getRealSize(wallHeightUnit);\r\n                // 远离边缘的距离\r\n                let farBorder = this.ctx.utils.getRealSize(farBorderUnit);\r\n                for (let i = 0; i < 4; i++) {\r\n                    const aroundWallGeometry = new THREE.PlaneGeometry(sizeX, wallHeight);\r\n                    const aroundWallMaterial = new THREE.MeshStandardMaterial({\r\n                        color: 0x808080,\r\n                        // side: THREE.DoubleSide, // 两面都显示\r\n                        transparent: true,\r\n                        opacity: 0.05,\r\n                    });\r\n                    const aroundWall = new THREE.Mesh(aroundWallGeometry, aroundWallMaterial);\r\n                    aroundWall.receiveShadow = true;\r\n                    // aroundWall.position.y = wallHeight / 2;\r\n\r\n                    if (i === 0) {\r\n                        aroundWall.position.z = wallHeight / 2;\r\n\r\n                        aroundWall.rotation.x = -Math.PI / 2;\r\n\r\n                        aroundWall.position.y = -sizeX / 2 + farBorder;\r\n                    }\r\n                    else if (i === 1) {\r\n                        aroundWall.position.z = wallHeight / 2;\r\n\r\n                        aroundWall.rotation.y = -Math.PI / 2;\r\n                        aroundWall.rotation.x = -Math.PI / 2;\r\n\r\n                        aroundWall.position.x = sizeX / 2 - farBorder;\r\n                    }\r\n                    else if (i === 2) {\r\n                        aroundWall.position.z = wallHeight / 2;\r\n\r\n                        aroundWall.rotation.x = Math.PI / 2;\r\n\r\n                        aroundWall.position.y = sizeX / 2 + farBorder;\r\n                    }\r\n                    else if (i === 3) {\r\n                        aroundWall.position.z = wallHeight / 2;\r\n\r\n                        aroundWall.rotation.y = Math.PI / 2;\r\n                        aroundWall.rotation.x = Math.PI / 2;\r\n\r\n                        aroundWall.position.x = -sizeX / 2 + farBorder;\r\n                    }\r\n\r\n                    ground.add(aroundWall);\r\n                }\r\n            }\r\n\r\n            ground.rotation.x = -Math.PI / 2;\r\n\r\n            return ground;\r\n        }\r\n        else if (type === 'wall') {\r\n\r\n        }\r\n    }\r\n}\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/model/part/ModelCreatorPart.js?\n}");

/***/ }),

/***/ "./src/model/part/PhysBodyCreatorPart.js":
/*!***********************************************!*\
  !*** ./src/model/part/PhysBodyCreatorPart.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PhysBodyCreatorPart: () => (/* binding */ PhysBodyCreatorPart)\n/* harmony export */ });\n/**\r\n * 物理体创建者\r\n */\r\nclass PhysBodyCreatorPart {\r\n    constructor(ctx, data) {\r\n        this.ctx = ctx;\r\n        this.data = data;\r\n    }\r\n\r\n    build(pars) {\r\n        const { THREE, CANNON } = this.ctx.imports;\r\n        const { debug, scale, box, mass, children, restitution, friction } = pars;\r\n\r\n        const physBody = new CANNON.Body({ mass: mass || 0 }); // 静态物体\r\n        this.ctx.physWorld.addBody(physBody);\r\n\r\n        const size = box.getSize(new THREE.Vector3());\r\n\r\n        let boxSizeX = size.x * scale;\r\n        let boxSizeY = size.y * scale;\r\n        let boxsizeZ = size.z * scale;\r\n\r\n        // 根据组合方式创建物理体\r\n        if (children) {\r\n            if (debug) console.log('创建物理体b')\r\n            for (const i in children) {\r\n                const child = children[i];\r\n\r\n                let shape;\r\n                // 球型\r\n                if (child.type === 'sphere') {\r\n                    const childScale = child.scale || 1;\r\n                    let childShapeSizeX = boxSizeX * childScale;\r\n                    let childShapeSizeY = boxSizeY * childScale;\r\n                    let childShapeSizeZ = boxsizeZ * childScale;\r\n                    let posY = 0;\r\n                    if (child.posYType === 1) {\r\n                        posY = childShapeSizeY / 2 * (child.offsetYScale || 1);\r\n                    }\r\n\r\n                    const radius = Math.min(childShapeSizeX, childShapeSizeZ) / 2;\r\n                    shape = new CANNON.Sphere(radius);\r\n                    physBody.addShape(shape, new CANNON.Vec3(0, posY, 0));\r\n                }\r\n                // 方形\r\n                else {\r\n                    const childScale = child.scale || 1;\r\n                    let childShapeSizeX = boxSizeX * childScale;\r\n                    let childShapeSizeY = boxSizeY * childScale;\r\n                    let childShapeSizeZ = boxsizeZ * childScale;\r\n                    let posY = 0;\r\n                    if (child.posYType === 1) {\r\n                        posY = childShapeSizeY / 2 * (child.offsetYScale || 1);\r\n                    }\r\n\r\n                    shape = new CANNON.Box(new CANNON.Vec3(childShapeSizeX / 2, childShapeSizeY / 2, childShapeSizeZ / 2));\r\n                    physBody.addShape(shape, new CANNON.Vec3(0, posY, 0));\r\n                }\r\n            }\r\n        }\r\n        // 根据边界盒子创建物理体\r\n        else {\r\n            if (debug) console.log('创建物理体a')\r\n            // 创建形状\r\n            const shape0 = new CANNON.Box(new CANNON.Vec3(boxSizeX / 2, boxSizeY / 2, boxsizeZ / 2));\r\n            physBody.addShape(shape0, new CANNON.Vec3(0, 0, 0));\r\n        }\r\n\r\n        // 设置树的物理材质\r\n        physBody.material = new CANNON.Material();\r\n        physBody.material.restitution = restitution || 0.3; // 弹性\r\n        physBody.material.friction = friction || 0.8; // 摩擦力\r\n\r\n        return physBody;\r\n    }\r\n}\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/model/part/PhysBodyCreatorPart.js?\n}");

/***/ }),

/***/ "./src/service/ProjectStorageService.js":
/*!**********************************************!*\
  !*** ./src/service/ProjectStorageService.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProjectStorageService: () => (/* binding */ ProjectStorageService)\n/* harmony export */ });\nclass ProjectStorageService {\r\n    constructor(ctx) {\r\n        this.ctx = ctx;\r\n\r\n        this.projectStorageMapper = ctx.projectStorageMapper;\r\n    }\r\n\r\n    async save() {\r\n        console.log(`保存项目...`)\r\n        await this.projectStorageMapper.save(this.ctx.data.project.id, this.ctx.data.project.name, this._convertProjectToData());\r\n        this.ctx.utils.outputDevTip('保存项目成功');\r\n    }\r\n\r\n    async load(opts) {\r\n        const project = await this.ctx.projectStorageMapper.load(this.ctx.data.project.id);\r\n        if (project) {\r\n            opts.beforeMainStartItems = project.data.beforeMainStartItems;\r\n            opts.afterMainStartItems = project.data.afterMainStartItems;\r\n        }\r\n    }\r\n\r\n    _convertProjectToData() {\r\n        const { ctx } = this;\r\n\r\n        const data = {\r\n            beforeMainStartItems: [\r\n                /*** 设计师配置 ***/\r\n                {\r\n                    type: 'ctx-init',\r\n                    onlyDevViewport: true,\r\n                    data: {\r\n                        canExposeContext: true,\r\n                        showFPS: true,\r\n                        showMemory: true,\r\n                    }\r\n                },\r\n                {\r\n                    type: 'camera-init',\r\n                    onlyDevViewport: true,\r\n                    data: ctx.data.camera,\r\n                },\r\n                /*** 玩家配置 ***/\r\n                {\r\n                    type: 'ctx-init',\r\n                    onlyViewport: true,\r\n                    data: {\r\n                        showFPS: true,\r\n                        showMemory: true,\r\n                    }\r\n                },\r\n                /*** 通用配置 ***/\r\n                {\r\n                    type: 'world-init',\r\n                    data: {\r\n                        unitSize: ctx.data.world.unitSize,\r\n                        gravityUnit: ctx.data.world.gravityUnit,\r\n                    }\r\n                },\r\n                {\r\n                    type: 'player-init',\r\n                    data: {\r\n                        // 初始位置\r\n                        startPosUnit: ctx.data.player.startPosUnit,\r\n                        // 初始朝向点\r\n                        startLookAtPosUnit: ctx.data.player.startLookAtPosUnit,\r\n                        // 玩家身高，1.75米\r\n                        heightUnit: ctx.data.player.heightUnit,\r\n                        moveSpeedUnit: ctx.data.player.moveSpeedUnit,\r\n                        fastMoveSpeedUnit: ctx.data.player.fastMoveSpeedUnit,\r\n                        jumpStrengthUnit: ctx.data.player.jumpStrengthUnit,\r\n\r\n                        showCCI: ctx.data.player.showCCI,\r\n                    }\r\n                },\r\n            ],\r\n            afterMainStartItems: [\r\n                // /*** 设计师配置 ***/\r\n                // // 创建世界网格\r\n                // {\r\n                //     type: 'world-grid-build',\r\n                //     onlyDevViewport: true,\r\n                //     data: {\r\n                //         __autoSave: false,\r\n                //         eachGridSizeUnit: 1,\r\n                //         totalGridCount: 51,\r\n                //         colorCenterLine: 'blue',\r\n                //         colorGrid: 'silver',\r\n                //         pos: [0, 0.5, 0],\r\n                //     }\r\n                // },\r\n                // // 创建中心坐标指示器\r\n                // {\r\n                //     type: 'object-build',\r\n                //     onlyDevViewport: (this.ctx.data.player.showCenterIndicator ? null : true),\r\n                //     data: {\r\n                //         __autoSave: false,\r\n                //         helper: {\r\n                //             type: 'center-axes-indicator'\r\n                //         }\r\n                //     }\r\n                // },\r\n                // // 创建第一人称视角控制能力（设计师）\r\n                // {\r\n                //     type: 'FirstPersonControlsAbility-ability-build',\r\n                //     onlyDevViewport: true,\r\n                //     name: 'first_person_controls',\r\n                //     data: {\r\n                //         __autoSave: false,\r\n                //         gravity: 0,\r\n                //         collisionEnabled: false,\r\n                //         upDownEnabled: true,\r\n                //     }\r\n                // },\r\n                // // 创建选中游戏对象能力\r\n                // {\r\n                //     type: 'SelectCGObjectAbility-ability-build',\r\n                //     onlyDevViewport: true,\r\n                //     name: 'select-cgo-dev',\r\n                //     data: {\r\n                //         __autoSave: false,\r\n                //         singleSelectMode: true,\r\n                //     }\r\n                // },\r\n                // // 创建移动选中对象能力\r\n                // {\r\n                //     type: 'MoveSelectedCGObjectAbility-ability-build',\r\n                //     onlyDevViewport: true,\r\n                //     name: 'move-selected-cgo-dev',\r\n                //     data: {\r\n                //         __autoSave: false,\r\n                //     }\r\n                // },\r\n                // /*** 玩家配置 ***/\r\n                // // 创建第一人称视角控制能力（玩家）\r\n                // {\r\n                //     type: 'FirstPersonControlsAbility-ability-build',\r\n                //     onlyViewport: true,\r\n                //     name: 'first_person_controls',\r\n                //     data: {\r\n                //         __autoSave: false,\r\n                //     }\r\n                // },\r\n                // /*** 通用配置 ***/\r\n                // // 创建天空\r\n                // {\r\n                //     type: 'sky-object-build',\r\n                //     data: {\r\n                //         __autoSave: false,\r\n                //     }\r\n                // },\r\n                // // 创建光源\r\n                // {\r\n                //     type: 'light-object-build',\r\n                //     data: {\r\n                //         __autoSave: false,\r\n                //         ambientLight: {\r\n                //         }\r\n                //     }\r\n                // },\r\n                // // 创建用户控制能力\r\n                // {\r\n                //     type: 'UserControlsAbility-ability-build',\r\n                //     name: 'user_ctrl',\r\n                //     data: {\r\n                //         __autoSave: false,\r\n                //     }\r\n                // },\r\n                // // 创建获取用户变动事件能力\r\n                // {\r\n                //     type: 'GetUserChangeAnyEventAbility-ability-build',\r\n                //     name: 'get_user_change_any_event',\r\n                //     data: {\r\n                //         __autoSave: false,\r\n                //     }\r\n                // },\r\n            ],\r\n        };\r\n\r\n        // // 生成动态添加的游戏对象\r\n        // const cgoList = this.ctx.cgWorld.getObjects();\r\n        // for (const cgo of cgoList) {\r\n        //     if (cgo.data.__autoSave !== false) {\r\n        //         data.afterMainStartItems.push({\r\n        //             type: `${cgo.constructor.name}-object-build`,\r\n        //             data: cgo.data,\r\n        //         })\r\n        //     }\r\n        // }\r\n\r\n        return data;\r\n    }\r\n}\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/service/ProjectStorageService.js?\n}");

/***/ }),

/***/ "./src/service/SandboxConnection.js":
/*!******************************************!*\
  !*** ./src/service/SandboxConnection.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SandboxConnection: () => (/* binding */ SandboxConnection)\n/* harmony export */ });\n/* harmony import */ var _model_CGSkyModel__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../model/CGSkyModel */ \"./src/model/CGSkyModel.js\");\n/* harmony import */ var _ability_FirstPersonControlsAbility__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../ability/FirstPersonControlsAbility */ \"./src/ability/FirstPersonControlsAbility.js\");\n/* harmony import */ var _ability_UserControlsAbility__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ability/UserControlsAbility */ \"./src/ability/UserControlsAbility.js\");\n/* harmony import */ var _ability_GetUserChangeAnyEventAbility__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ability/GetUserChangeAnyEventAbility */ \"./src/ability/GetUserChangeAnyEventAbility.js\");\n/* harmony import */ var _model_CG2DGridModel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../model/CG2DGridModel */ \"./src/model/CG2DGridModel.js\");\n\r\n\r\n\r\n\r\n\r\n\r\n/**\r\n * 沙盒连接，用于给编辑器访问沙盒\r\n */\r\nclass SandboxConnection {\r\n\r\n    constructor(ctx) {\r\n        this.ctx = ctx;\r\n\r\n        this._devTipEventListeners = [];\r\n    }\r\n\r\n    /**\r\n     * 初始化\r\n     * @param data\r\n     * @returns {Promise<void>}\r\n     */\r\n    async init(data = {}) {\r\n        if (data.projectId != null) {\r\n            this.ctx.data.project.id = data.projectId;\r\n        }\r\n        if (data.projectName != null) {\r\n            this.ctx.data.project.name = data.projectName;\r\n        }\r\n\r\n        if (data.showFPS != null) {\r\n            this.ctx.showFPS = data.showFPS;\r\n            this.ctx.utils.showFPSMonitor();\r\n        }\r\n        if (data.showMemory != null) {\r\n            this.ctx.showMemory = data.showMemory;\r\n            this.ctx.utils.showMemoryMonitor();\r\n        }\r\n\r\n        return {\r\n\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 开启天空盒子\r\n     * @param data\r\n     * @returns {Promise<void>}\r\n     */\r\n    async openSkyBox(data = {}) {\r\n        await this.ctx.cgWorld.addObject(new _model_CGSkyModel__WEBPACK_IMPORTED_MODULE_0__.CGSkyModel(this.ctx, data));\r\n    }\r\n\r\n    /**\r\n     * 开启开发者相机控制\r\n     * @param data\r\n     * @returns {Promise<void>}\r\n     */\r\n    async openDevCameraControls(data = {}) {\r\n        await this.ctx.cgWorld.addAbility(new _ability_FirstPersonControlsAbility__WEBPACK_IMPORTED_MODULE_1__.FirstPersonControlsAbility(this.ctx, data), 'first_person_controls')\r\n    }\r\n\r\n    /**\r\n     * 开启开发者网格\r\n     * @param data\r\n     * @returns {Promise<void>}\r\n     */\r\n    async openDevGrid(data = {}) {\r\n        this.ctx.data.designer.groundGrid.eachGridSize = this.ctx.utils.getRealSize(data.eachGridSizeUnit);\r\n        this.ctx.data.designer.groundGrid.totalGridCount = data.totalGridCount;\r\n        await this.ctx.cgWorld.addObject(new _model_CG2DGridModel__WEBPACK_IMPORTED_MODULE_4__.CG2DGridModel(this.ctx, data));\r\n    }\r\n\r\n    /**\r\n     * 添加开发者提示事件监听\r\n     * @param func\r\n     */\r\n    addDevTipEventListener(func) {\r\n        this._devTipEventListeners.push({\r\n            func,\r\n        });\r\n    }\r\n\r\n    async build() {\r\n        await this.ctx.cgWorld.addAbility(new _ability_UserControlsAbility__WEBPACK_IMPORTED_MODULE_2__.UserControlsAbility(this.ctx, {}), 'user_ctrl');\r\n        await this.ctx.cgWorld.addAbility(new _ability_GetUserChangeAnyEventAbility__WEBPACK_IMPORTED_MODULE_3__.GetUserChangeAnyEventAbility(this.ctx, {}), 'get_user_change_any_event');\r\n\r\n        await this.ctx.cgWorld.startAbilites();\r\n        await this.ctx.cgWorld.buildObjects();\r\n    }\r\n\r\n    isReady() {\r\n        return this.ctx.isReady;\r\n    }\r\n\r\n    /**\r\n     * 输出开发者提示，例如：项目保存成功\r\n     * @param msg\r\n     */\r\n    showDevTip(msg) {\r\n        this._devTipEventListeners.forEach((n) => {\r\n            n.func(msg);\r\n        })\r\n    }\r\n}\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/service/SandboxConnection.js?\n}");

/***/ }),

/***/ "./src/util/game-grid-util.js":
/*!************************************!*\
  !*** ./src/util/game-grid-util.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getXZGridPosByRealPos: () => (/* binding */ getXZGridPosByRealPos),\n/* harmony export */   getXZGridRealPosByRealPos: () => (/* binding */ getXZGridRealPosByRealPos),\n/* harmony export */   getXZRealPosByGridPos: () => (/* binding */ getXZRealPosByGridPos)\n/* harmony export */ });\n/**\r\n * 根据网格坐标获取真实坐标（此网格中心点坐标）\r\n * @param {*} eachGridSize \r\n * @param {*} totalGridCount \r\n * @param {*} gPos \r\n * @returns {{x: number, y: number, z: number}}\r\n */\r\nfunction getXZRealPosByGridPos(eachGridSize, totalGridCount, gPos) {\r\n    let x = 0, y = 0, z = 0;\r\n    if (gPos instanceof Array) {\r\n        x = gPos[0];\r\n        z = gPos[2];\r\n    } else {\r\n        x = gPos.x;\r\n        z = gPos.z;\r\n    }\r\n\r\n    // 将起点从左下角移动到中心位置（此时还是偏右上半格位置）\r\n    let gPosX = x + totalGridCount / 2;\r\n    let gPosZ = z + totalGridCount / 2;\r\n\r\n    const realX = (gPosX - totalGridCount / 2) * eachGridSize + eachGridSize / 2;\r\n    const realZ = (gPosZ - totalGridCount / 2) * eachGridSize + eachGridSize / 2;\r\n\r\n    // 向左下移动半格\r\n    return { x: realX - eachGridSize / 2, y: 0, z: realZ - eachGridSize / 2 };\r\n}\r\n\r\n/**\r\n * 根据真实坐标获取网格坐标\r\n * @param eachGridSize\r\n * @param totalGridCount\r\n * @param pos\r\n * @returns {{x: number, y: number, z: number}}\r\n */\r\nfunction getXZGridPosByRealPos(eachGridSize, totalGridCount, pos) {\r\n    let x = 0, y = 0, z = 0;\r\n    if (pos instanceof Array) {\r\n        x = pos[0];\r\n        z = pos[2];\r\n    } else {\r\n        x = pos.x;\r\n        z = pos.z;\r\n    }\r\n\r\n    // 将真实坐标转换为左下角为起点的坐标系统\r\n    const shiftedX = x + (totalGridCount / 2) * eachGridSize;\r\n    const shiftedZ = z + (totalGridCount / 2) * eachGridSize;\r\n\r\n    // 通过真实坐标计算网格坐标，并取整，使得网格坐标为整数\r\n    const gPosX = Math.floor(shiftedX / eachGridSize) - Math.floor(totalGridCount / 2);\r\n    const gPosZ = Math.floor(shiftedZ / eachGridSize) - Math.floor(totalGridCount / 2);\r\n\r\n    return { x: gPosX, y: 0, z: gPosZ };\r\n}\r\n\r\nfunction getXZGridRealPosByRealPos(eachGridSize, totalGridCount, pos) {\r\n    const gPos = getXZGridPosByRealPos(eachGridSize, totalGridCount, pos);\r\n    return getXZRealPosByGridPos(eachGridSize, totalGridCount, gPos);\r\n}\r\n\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/util/game-grid-util.js?\n}");

/***/ }),

/***/ "./src/util/http-util.js":
/*!*******************************!*\
  !*** ./src/util/http-util.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reqPostJson: () => (/* binding */ reqPostJson)\n/* harmony export */ });\n/**\r\n * 用POST发送json内容的http请求\r\n * 使用原生方法实现\r\n * @param {*} url \r\n * @param {*} data \r\n * @param {*} opts \r\n */\r\nfunction reqPostJson(url, data, opts = {}) {\r\n    const options = {\r\n        method: 'POST',\r\n        headers: {\r\n            'Content-Type': 'application/json',\r\n            ...opts.headers\r\n        },\r\n        body: JSON.stringify(data),\r\n        ...opts\r\n    };\r\n    return fetch(url, options)\r\n        .then(response => {\r\n            if (!response.ok) {\r\n                throw new Error('Network response was not ok');\r\n            }\r\n            return response.json();\r\n        });\r\n}\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/util/http-util.js?\n}");

/***/ }),

/***/ "./src/util/id-util.js":
/*!*****************************!*\
  !*** ./src/util/id-util.js ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   newCUIdA: () => (/* binding */ newCUIdA),\n/* harmony export */   newGuid: () => (/* binding */ newGuid),\n/* harmony export */   newId: () => (/* binding */ newId)\n/* harmony export */ });\n/*** id生成、随机数 ***/\r\n// yyyyMMddHHmmssSSS\r\nfunction newId() {\r\n    const now = new Date();\r\n    const zero = '0';\r\n    let id = '';\r\n    /*1*/\r\n    id = id.concat(now.getFullYear());\r\n    // id += Math.random().toString().replace('0.', '').substr(0, 4);\r\n    /*2*/\r\n    if ((now.getMonth() + 1) < 10) {\r\n        id = id.concat(zero).concat(now.getMonth() + 1);\r\n    }\r\n    else {\r\n        id = id.concat(now.getMonth() + 1);\r\n    }\r\n    if (now.getDate() < 10) {\r\n        id = id.concat(zero).concat(now.getDate());\r\n    }\r\n    else {\r\n        id = id.concat(now.getDate());\r\n    }\r\n    // id += Math.random().toString().replace('0.', '').substr(0, 4);\r\n    /*3*/\r\n    if (now.getHours() < 10) {\r\n        id = id.concat(zero).concat(now.getHours());\r\n    }\r\n    else {\r\n        id = id.concat(now.getHours());\r\n    }\r\n    if (now.getMinutes() < 10) {\r\n        id = id.concat(zero).concat(now.getMinutes());\r\n    }\r\n    else {\r\n        id = id.concat(now.getMinutes());\r\n    }\r\n    // id += Math.random().toString().replace('0.', '').substr(0, 4);\r\n    /*4*/\r\n    if (now.getSeconds() < 10) {\r\n        id = id.concat(zero).concat(now.getSeconds());\r\n    }\r\n    else {\r\n        id = id.concat(now.getSeconds());\r\n    }\r\n    if (now.getMilliseconds() < 10) {\r\n        id = id.concat(zero).concat(now.getMilliseconds());\r\n    }\r\n    else {\r\n        var mStr = now.getMilliseconds().toString();\r\n        id = id.concat(mStr.substr(mStr.length - 3, 3));\r\n    }\r\n    // id += Math.random().toString().replace('0.', '').substr(0, 4);\r\n    return id;\r\n}\r\n\r\nfunction newGuid() {\r\n    function s4() { return Math.floor((1 + Math.random()) * 0x10000).toString(16).substring(1); }\r\n    return s4().concat(s4()).concat('-').concat(s4()).concat('-').concat(s4()).concat('-').concat(s4()).concat('-')\r\n        .concat(s4()).concat(s4()).concat(s4());\r\n}\r\n\r\n// yyyyMMddHHmmssSSS-fhke21\r\nfunction newCUIdA() {\r\n    var str0 = newId();\r\n    var str1 = newGuid().substr(0, 6)\r\n    return str0.concat(\"-\").concat(str1);\r\n}\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/util/id-util.js?\n}");

/***/ }),

/***/ "./src/util/import-fbx-model-util.js":
/*!*******************************************!*\
  !*** ./src/util/import-fbx-model-util.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   importFBXModel: () => (/* binding */ importFBXModel),\n/* harmony export */   importFBXModel_0: () => (/* binding */ importFBXModel_0)\n/* harmony export */ });\n\r\n\r\n/**\r\n * 导入fbx格式的模型\r\n * @param {*} dirUrl 模型存放目录（包含base.fbx、shaded.png、texture_diffuse.png、texture_normal.png、texture_metallic.png、texture_roughness.png、texture_pbr.png）\r\n\r\n * @param {*} opts \r\n * @returns \r\n */\r\nasync function importFBXModel(ctx, dirUrl, opts = {}) {\r\n    const { THREE, FBXLoader } = ctx.imports;\r\n    let baseFileName = opts.baseFileName || 'base_basic_pbr.fbx'; // base.fbx | base_basic_pbr.fbx | base_basic_shaded.fbx\r\n    let baseFilePath = `${dirUrl}/${baseFileName}`;\r\n\r\n    const manager = new THREE.LoadingManager();\r\n    const loader = new FBXLoader(manager);\r\n\r\n    console.log(`加载模型：${baseFilePath}`)\r\n\r\n    const model = await new Promise((resolve) => {\r\n        loader.load(\r\n            baseFilePath, // 替换为你的模型文件路径\r\n            function (object) {\r\n                if (opts.debug) console.log('模型加载成功');\r\n\r\n                // 手动加载纹理\r\n                const textureLoader = new THREE.TextureLoader();\r\n                \r\n                // 加载所有纹理\r\n                const textures = {\r\n                    shaded: textureLoader.load(`${dirUrl}/shaded.png`),\r\n                    diffuse: textureLoader.load(`${dirUrl}/texture_diffuse.png`),\r\n                    normal: textureLoader.load(`${dirUrl}/texture_normal.png`),\r\n                    metallic: textureLoader.load(`${dirUrl}/texture_metallic.png`),\r\n                    roughness: textureLoader.load(`${dirUrl}/texture_roughness.png`),\r\n                    pbr: textureLoader.load(`${dirUrl}/texture_pbr.png`)\r\n                };\r\n                \r\n                // 设置纹理加载回调\r\n                Object.keys(textures).forEach(key => {\r\n                    textures[key].onLoad = function() {\r\n                        if (opts.debug) console.log(`${key}纹理加载成功`);\r\n                    };\r\n                    textures[key].onError = function(error) {\r\n                        if (opts.debug) console.error(`${key}纹理加载失败：`, error);\r\n                    };\r\n                });\r\n\r\n                // 检查并修复材质\r\n                object.traverse((child) => {\r\n                    // 注意：如果导入的模型里已经做了空气墙，可以通过命令筛选的方式来获取这些空气墙对象，例：collider_xxx\r\n                    // console.log(child.name, child.isMesh)\r\n\r\n                    if (child.isMesh) {\r\n                        child.castShadow = true;\r\n                        child.receiveShadow = true;\r\n                        if (opts.debug) console.log('网格名称:', child.name);\r\n                        if (opts.debug) console.log('材质信息:', child.material);\r\n\r\n                        const applyTextures = (material) => {\r\n                            // 应用基础纹理\r\n                            if (!material.map) {\r\n                                material.map = textures.diffuse || textures.shaded;\r\n                            }\r\n                            \r\n                            // 应用法线贴图\r\n                            if (textures.normal) {\r\n                                material.normalMap = textures.normal;\r\n                            }\r\n                            \r\n                            // 应用金属度贴图\r\n                            if (textures.metallic) {\r\n                                material.metalnessMap = textures.metallic;\r\n                                material.metalness = 1.0; // 设置金属度基础值\r\n                            }\r\n                            \r\n                            // 应用粗糙度贴图\r\n                            if (textures.roughness) {\r\n                                material.roughnessMap = textures.roughness;\r\n                                material.roughness = 1.0; // 设置粗糙度基础值\r\n                            }\r\n                            \r\n                            // 如果有PBR贴图，可以用作环境遮蔽贴图\r\n                            if (textures.pbr) {\r\n                                material.aoMap = textures.pbr;\r\n                                material.aoMapIntensity = 1.0;\r\n                            }\r\n                            \r\n                            material.needsUpdate = true;\r\n                        };\r\n\r\n                        if (child.material) {\r\n                            // 如果是数组材质\r\n                            if (Array.isArray(child.material)) {\r\n                                child.material.forEach((mat) => {\r\n                                    applyTextures(mat);\r\n                                });\r\n                            } else {\r\n                                // 单个材质\r\n                                applyTextures(child.material);\r\n                            }\r\n                        }\r\n                    }\r\n                });\r\n\r\n                resolve(object);\r\n            },\r\n            function (xhr) {\r\n                // 加载进度回调（可选）\r\n                if (opts.debug) console.log((xhr.loaded / xhr.total * 100) + '% loaded');\r\n            },\r\n            function (error) {\r\n                // 加载错误回调（可选）\r\n                if (opts.debug) console.error('An error happened', error);\r\n            }\r\n        );\r\n    });\r\n\r\n    return {\r\n        model,\r\n    };\r\n}\r\n\r\n/**\r\n * 导入fbx格式的模型（简单）\r\n * @param {*} dirUrl 模型存放目录（包含base.fbx、shaded.png）\r\n * @param {*} opts \r\n * @returns \r\n */\r\nasync function importFBXModel_0(ctx, dirUrl, opts = {}) {\r\n    const { THREE, FBXLoader } = ctx.imports;\r\n\r\n    const manager = new THREE.LoadingManager();\r\n    const loader = new FBXLoader(manager);\r\n    const model = await new Promise((resolve) => {\r\n        loader.load(\r\n            `${dirUrl}/base.fbx`, // 替换为你的模型文件路径\r\n            function (object) {\r\n                if (opts.debug) console.log('模型加载成功');\r\n\r\n                // 手动加载纹理\r\n                const textureLoader = new THREE.TextureLoader();\r\n                const texture = textureLoader.load(`${dirUrl}/shaded.png`,\r\n                    function (texture) {\r\n                        if (opts.debug) console.log('纹理加载成功。', texture);\r\n                    },\r\n                    function (progress) {\r\n                        if (opts.debug) console.log('纹理加载进度：', progress);\r\n                    },\r\n                    function (error) {\r\n                        if (opts.debug) console.error('纹理加载失败：', error);\r\n                    }\r\n                );\r\n\r\n                // 检查并修复材质\r\n                object.traverse((child) => {\r\n                    // 注意：如果导入的模型里已经做了空气墙，可以通过命令筛选的方式来获取这些空气墙对象，例：collider_xxx\r\n                    // console.log(child.name, child.isMesh)\r\n\r\n                    if (child.isMesh) {\r\n                        child.castShadow = true;\r\n                        child.receiveShadow = true;\r\n                        if (opts.debug) console.log('网格名称:', child.name);\r\n                        if (opts.debug) console.log('材质信息:', child.material);\r\n\r\n                        if (child.material) {\r\n                            // 如果是数组材质\r\n                            if (Array.isArray(child.material)) {\r\n                                child.material.forEach((mat, index) => {\r\n                                    if (opts.debug) console.log(`材质${index}:`, mat);\r\n                                    if (!mat.map) {\r\n                                        mat.map = texture;\r\n                                        mat.needsUpdate = true;\r\n                                    }\r\n                                });\r\n                            } else {\r\n                                // 单个材质\r\n                                if (!child.material.map) {\r\n                                    child.material.map = texture;\r\n                                    child.material.needsUpdate = true;\r\n                                    if (opts.debug) console.log('已应用纹理到材质');\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                });\r\n\r\n                resolve(object);\r\n            },\r\n            function (xhr) {\r\n                // 加载进度回调（可选）\r\n                if (opts.debug) console.log((xhr.loaded / xhr.total * 100) + '% loaded');\r\n            },\r\n            function (error) {\r\n                // 加载错误回调（可选）\r\n                if (opts.debug) console.error('An error happened', error);\r\n            }\r\n        );\r\n    });\r\n\r\n    return {\r\n        model,\r\n    };\r\n}\r\n\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/util/import-fbx-model-util.js?\n}");

/***/ }),

/***/ "./src/util/tjs-axes-util.js":
/*!***********************************!*\
  !*** ./src/util/tjs-axes-util.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tjs_createAxesIndicator: () => (/* binding */ tjs_createAxesIndicator),\n/* harmony export */   tjs_destroyAxesIndicator: () => (/* binding */ tjs_destroyAxesIndicator)\n/* harmony export */ });\n/**\r\n * 创建坐标指示器\r\n * @param {*} ctx \r\n * @param {*} opts \r\n */\r\nfunction tjs_createAxesIndicator(ctx, opts = {}) {\r\n    const { THREE, CSS2DObject } = ctx.imports;\r\n\r\n    let caiAxesSize = opts.caiAxesSize;\r\n    let caiArrowRadius = opts.caiArrowRadius;\r\n\r\n    // 中心点坐标指示器尺寸\r\n    if (caiAxesSize == null) caiAxesSize = 200;\r\n    if (caiArrowRadius == null) caiArrowRadius = 1;\r\n\r\n    let arrowLabelDistance = opts.arrowLabelDistance || 5;\r\n\r\n    const axesHelper = new THREE.AxesHelper(caiAxesSize);\r\n    axesHelper.name = 'axesHelper';\r\n\r\n    axesHelper.renderOrder = 1;\r\n    axesHelper.position.set(0, 0, 0);\r\n\r\n    {\r\n        // x轴箭头（红色）\r\n        const geometry = new THREE.ConeGeometry(caiArrowRadius, caiAxesSize / 5, 32);\r\n        const material = new THREE.MeshBasicMaterial({ color: 'red' });\r\n        const cone = new THREE.Mesh(geometry, material);\r\n        cone.position.set(caiAxesSize, 0, 0);\r\n        cone.rotateZ(-Math.PI / 180 * 90);\r\n        axesHelper.add(cone);\r\n\r\n        // x轴标签\r\n        const labelDiv = document.createElement('div');\r\n        labelDiv.textContent = '+X';\r\n        labelDiv.style = 'color:silver;';\r\n        const css2dLabel = new CSS2DObject(labelDiv);\r\n        css2dLabel.position.set(caiAxesSize + arrowLabelDistance, 0, 0);\r\n        axesHelper.add(css2dLabel);\r\n    }\r\n    {\r\n        // y轴箭头（绿色）\r\n        const geometry = new THREE.ConeGeometry(caiArrowRadius, caiAxesSize / 5, 32);\r\n        const material = new THREE.MeshBasicMaterial({ color: 'green' });\r\n        const cone = new THREE.Mesh(geometry, material);\r\n        cone.position.set(0, caiAxesSize, 0);\r\n        axesHelper.add(cone);\r\n\r\n        // y轴标签\r\n        const labelDiv = document.createElement('div');\r\n        labelDiv.textContent = '+Y';\r\n        labelDiv.style = 'color:silver;';\r\n        const css2dLabel = new CSS2DObject(labelDiv);\r\n        css2dLabel.position.set(0, caiAxesSize + arrowLabelDistance, 0);\r\n        axesHelper.add(css2dLabel);\r\n    }\r\n    {\r\n        // z轴箭头（蓝色）\r\n        const geometry = new THREE.ConeGeometry(caiArrowRadius, caiAxesSize / 5, 32);\r\n        const material = new THREE.MeshBasicMaterial({ color: 'blue' });\r\n        const cone = new THREE.Mesh(geometry, material);\r\n        cone.position.set(0, 0, caiAxesSize);\r\n        cone.rotateX(Math.PI / 180 * 90);\r\n        axesHelper.add(cone);\r\n\r\n        // z轴标签\r\n        const labelDiv = document.createElement('div');\r\n        labelDiv.textContent = '+Z';\r\n        labelDiv.style = 'color:silver;';\r\n        const css2dLabel = new CSS2DObject(labelDiv);\r\n        css2dLabel.position.set(0, 0, caiAxesSize + arrowLabelDistance);\r\n        axesHelper.add(css2dLabel);\r\n    }\r\n\r\n    return axesHelper;\r\n}\r\n\r\nfunction tjs_destroyAxesIndicator(axesHelper) {\r\n    axesHelper.traverse(obj => {\r\n        if (obj.isCSS2DObject && obj.element) {\r\n            obj.element.remove(); // 从 DOM 中移除标签\r\n        }\r\n        if (obj.geometry) obj.geometry.dispose();\r\n        if (obj.material) obj.material.dispose();\r\n    });\r\n    if (axesHelper.parent) {\r\n        axesHelper.parent.remove(axesHelper);\r\n    }\r\n}\r\n\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/util/tjs-axes-util.js?\n}");

/***/ }),

/***/ "./src/util/tjs-camera-util.js":
/*!*************************************!*\
  !*** ./src/util/tjs-camera-util.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tjs_calCameraLookAtPoint: () => (/* binding */ tjs_calCameraLookAtPoint),\n/* harmony export */   tjs_getCameraToXZPos: () => (/* binding */ tjs_getCameraToXZPos)\n/* harmony export */ });\n/**\r\n * 获取相机朝向XY面的交叉点坐标\r\n * @param {*} ctx 上下文\r\n * @param {THREE.Camera} camera \r\n * @param {*} y 固定值\r\n * @returns {[number, number, number]} 在XY面上的坐标，例：[12, 0, 66]\r\n */\r\nfunction tjs_getCameraToXZPos(ctx, camera, y) {\r\n    const { THREE } = ctx.imports;\r\n    if (y == null) y = ctx.data.designer.focusGroundHeight;\r\n\r\n    // 创建一个射线，从相机位置指向相机的前方\r\n    const direction = new THREE.Vector3();\r\n    camera.getWorldDirection(direction);\r\n\r\n    // 创建射线起点（相机位置）\r\n    const origin = camera.position.clone();\r\n\r\n    // 计算射线与XZ平面（y固定）的交点\r\n    // 平面方程：y = 固定值\r\n    // 射线方程：origin + t * direction\r\n    // 求解t：origin.y + t * direction.y = 固定值\r\n    // t = (固定值 - origin.y) / direction.y\r\n\r\n    // 防止除以零（相机方向与XZ平面平行）\r\n    if (Math.abs(direction.y) < 0.0001) {\r\n        return [origin.x, y, origin.z]; // 返回相机在XZ平面上的投影点\r\n    }\r\n\r\n    // 计算t\r\n    const t = (y - origin.y) / direction.y;\r\n\r\n    // 如果t为负值，表示交点在相机后方\r\n    if (t < 0) {\r\n        return [origin.x, y, origin.z]; // 返回相机在XZ平面上的投影点\r\n    }\r\n\r\n    // 计算交点坐标\r\n    const intersectX = origin.x + t * direction.x;\r\n    const intersectZ = origin.z + t * direction.z;\r\n\r\n    return [intersectX, y, intersectZ];\r\n}\r\n\r\n/**\r\n * 获取摄像机朝向前的一个点\r\n * @param obj 摄像机实例\r\n * @param distance 起始点到目标点的距离\r\n * @return {Vector3}\r\n */\r\nfunction tjs_calCameraLookAtPoint(ctx, camera, distance) {\r\n    const { THREE } = ctx.imports;\r\n\r\n    const direction = new THREE.Vector3(); // 创建一个向量用于表示摄像机的朝向\r\n    camera.getWorldDirection(direction); // 获取摄像机的朝向\r\n    const position = new THREE.Vector3(); // 创建一个向量用于表示摄像机的位置\r\n    position.copy(camera.position); // 获取摄像机的位置\r\n    direction.multiplyScalar(distance); // 将方向向量缩放为指定的距离\r\n    position.add(direction); // 在摄像机位置上加上方向向量，得到目标点的位置\r\n    return position; // 返回目标点的位置\r\n}\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/util/tjs-camera-util.js?\n}");

/***/ }),

/***/ "./src/util/tjs-geometry-util.js":
/*!***************************************!*\
  !*** ./src/util/tjs-geometry-util.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tjs_createCirclePlane: () => (/* binding */ tjs_createCirclePlane),\n/* harmony export */   tjs_createCube: () => (/* binding */ tjs_createCube),\n/* harmony export */   tjs_createCylinder: () => (/* binding */ tjs_createCylinder),\n/* harmony export */   tjs_createCylinderByTwoPoint: () => (/* binding */ tjs_createCylinderByTwoPoint),\n/* harmony export */   tjs_createPlane: () => (/* binding */ tjs_createPlane),\n/* harmony export */   tjs_createSphere: () => (/* binding */ tjs_createSphere)\n/* harmony export */ });\n/**\r\n * 创建方形面\r\n * @param {*} ctx \r\n * @param {*} width 宽度\r\n * @param {*} height 高度\r\n */\r\nfunction tjs_createPlane(ctx, width, height, opts = {}) {\r\n    const { THREE } = ctx.imports;\r\n    let { color, side, opacity, receiveShadow, castShadow } = opts;\r\n    let texture = null;\r\n    let video = null;\r\n\r\n    // 显示的面\r\n    if (side == null) side = THREE.DoubleSide;\r\n    else if (side === 'front') side = THREE.FrontSide;\r\n    else if (side === 'back') side = THREE.BackSide;\r\n\r\n    // 图片纹理\r\n    if (opts.imageSource && opts.imageSource.length > 0) {\r\n        texture = new THREE.TextureLoader().load(opts.imageSource);\r\n    }\r\n\r\n    // 视频纹理\r\n    if (opts.videoSource && opts.videoSource.length > 0) {\r\n        video = document.createElement('video'); // 创建HTML视频元素\r\n        video.src = opts.videoSource;                 // 设置视频源\r\n        video.crossOrigin = \"anonymous\";\r\n        video.preload = 'auto';\r\n        video.loop = true;                           // 设置视频循环播放\r\n        video.muted = true;                          // 设置视频静音\r\n        // video.playsInline = true;                    // 设置视频内联播放，防止全屏\r\n        // video.autoplay = true;                       // 设置视频自动播放\r\n        // video.play();                                // 开始播放视频\r\n\r\n        // source = document.createElement('source');\r\n        // source.src = opts.videoSource;\r\n        // source.type = 'video/mp4';\r\n        // video.appendChild(source);\r\n\r\n        // 视频纹理\r\n        texture = new THREE.VideoTexture(video);\r\n    }\r\n\r\n    // 几何体\r\n    const geometry = new THREE.PlaneGeometry(width, height);\r\n    // 材质\r\n    const material = new THREE.MeshStandardMaterial({\r\n        map: texture,\r\n        color: color || 'silver',\r\n        // THREE.FrontSide | THREE.BackSide | THREE.DoubleSide\r\n        side: side, // 两面都显示\r\n        transparent: (opacity >= 0) || false,\r\n        opacity: opacity || 1,\r\n        // receiveShadow: receiveShadow || true,\r\n    });\r\n    // 网格对象\r\n    const mesh = new THREE.Mesh(geometry, material);\r\n\r\n    // 对齐点\r\n    if (opts.alignmentPointType === 'bottom') {\r\n        mesh.geometry.translate(0, (height / 2), 0);\r\n    }\r\n\r\n    if (receiveShadow != null) {\r\n        mesh.receiveShadow = receiveShadow;\r\n    }\r\n\r\n    if (castShadow != null) {\r\n        mesh.castShadow = castShadow;\r\n    }\r\n\r\n    if (opts.videoSource && opts.videoSource.length > 0) {\r\n        mesh.userData.video = video;\r\n    }\r\n\r\n    return mesh;\r\n}\r\n\r\n/**\r\n * 创建圆形面\r\n * @param {*} ctx \r\n * @param {*} radius 半径\r\n * @param {*} segments 分段数（越大越圆滑）\r\n * @param {*} opts \r\n * @returns \r\n */\r\nfunction tjs_createCirclePlane(ctx, radius, segments, opts = {}) {\r\n    const { THREE } = ctx.imports;\r\n    const { color, side, opacity, receiveShadow, castShadow } = opts;\r\n\r\n    // 几何体\r\n    // 参数：半径 = 1，分段数 = 32（越大越圆滑）\r\n    const geometry = new THREE.CircleGeometry(radius || 1, segments || 32);\r\n    // 材质\r\n    const material = new THREE.MeshStandardMaterial({\r\n        color: color || 0x00ff00,\r\n        side: side || THREE.DoubleSide, // 两面都显示\r\n        transparent: (opacity >= 0) || false,\r\n        opacity: opacity || 1,\r\n        // receiveShadow: receiveShadow || true,\r\n    });\r\n    // 网格对象\r\n    const mesh = new THREE.Mesh(geometry, material);\r\n\r\n    if (receiveShadow != null) {\r\n        mesh.receiveShadow = receiveShadow;\r\n    }\r\n\r\n    if (castShadow != null) {\r\n        mesh.castShadow = castShadow;\r\n    }\r\n\r\n    return mesh;\r\n}\r\n\r\n/**\r\n * 创建立方体\r\n * @param {*} ctx \r\n * @param {*} width 宽度\r\n * @param {*} height 高度\r\n * @param {*} depth 深度\r\n * @param {*} opts 选项\r\n * @returns \r\n */\r\nfunction tjs_createCube(ctx, width, height, depth, opts = {}) {\r\n    const { THREE } = ctx.imports;\r\n    const { color, opacity, receiveShadow, castShadow, widthSegments, heightSegments, depthSegments } = opts;\r\n\r\n    // 几何体\r\n    const geometry = new THREE.BoxGeometry(width || 1, height || 1, depth || 1, widthSegments, heightSegments, depthSegments);\r\n    // 材质\r\n    const material = new THREE.MeshStandardMaterial({\r\n        color: color || 0xffffff,\r\n        transparent: (opacity >= 0) || false,\r\n        opacity: opacity || 1,\r\n        // receiveShadow: receiveShadow || true,\r\n        // castShadow: castShadow || true,\r\n    });\r\n    // 网格对象\r\n    const mesh = new THREE.Mesh(geometry, material);\r\n\r\n    if (receiveShadow != null) {\r\n        mesh.receiveShadow = receiveShadow;\r\n    }\r\n\r\n    if (castShadow != null) {\r\n        mesh.castShadow = castShadow;\r\n    }\r\n\r\n    return mesh;\r\n}\r\n\r\n/**\r\n * 创建圆桶形状\r\n * @param {*} ctx \r\n * @param {*} topRadius 上底面的半径\r\n * @param {*} bottomRadius 下底面的半径\r\n * @param {*} height 高度\r\n * @param {*} opts \r\n */\r\nfunction tjs_createCylinder(ctx, topRadius, bottomRadius, height, opts = {}) {\r\n    const { THREE } = ctx.imports;\r\n    const { color, opacity, receiveShadow, castShadow, radialSegments } = opts;\r\n\r\n    // 几何体\r\n    const geometry = new THREE.CylinderGeometry(topRadius, bottomRadius, height, radialSegments || 16);\r\n    // 材质\r\n    const material = new THREE.MeshStandardMaterial({\r\n        color: color || 0xffffff,\r\n        transparent: (opacity >= 0) || false,\r\n        opacity: opacity || 1,\r\n        // wireframe: true,\r\n        // receiveShadow: receiveShadow || true,\r\n        // castShadow: castShadow || true,\r\n    });\r\n    // 网格对象\r\n    const mesh = new THREE.Mesh(geometry, material);\r\n\r\n    if (receiveShadow != null) {\r\n        mesh.receiveShadow = receiveShadow;\r\n    }\r\n\r\n    if (castShadow != null) {\r\n        mesh.castShadow = castShadow;\r\n    }\r\n\r\n    return mesh;\r\n}\r\n\r\n/**\r\n * 创建球体\r\n * @param {*} ctx \r\n * @param {*} radius 球体半径\r\n * @param {*} opts 选项\r\n * @returns \r\n */\r\nfunction tjs_createSphere(ctx, radius, opts = {}) {\r\n    const { THREE } = ctx.imports;\r\n    const { color, opacity, receiveShadow, castShadow, widthSegments, heightSegments } = opts;\r\n\r\n    // 几何体\r\n    // 参数：半径, 水平分段数, 垂直分段数\r\n    const geometry = new THREE.SphereGeometry(radius || 1, widthSegments || 32, heightSegments || 32);\r\n    // 材质\r\n    const material = new THREE.MeshStandardMaterial({\r\n        color: color || 0xffffff,\r\n        transparent: (opacity >= 0) || false,\r\n        opacity: opacity || 1,\r\n        // receiveShadow: receiveShadow || true,\r\n        // castShadow: castShadow || true,\r\n    });\r\n    // 网格对象\r\n    const mesh = new THREE.Mesh(geometry, material);\r\n\r\n    if (receiveShadow != null) {\r\n        mesh.receiveShadow = receiveShadow;\r\n    }\r\n\r\n    if (castShadow != null) {\r\n        mesh.castShadow = castShadow;\r\n    }\r\n\r\n    return mesh;\r\n}\r\n\r\n/**\r\n * 根据两个坐标点创建圆桶形状\r\n * @param {*} ctx \r\n * @param {*} topRadius 上底面的半径\r\n * @param {*} topPos 上底面的位置 Vector3\r\n * @param {*} bottomRadius 下底面的半径\r\n * @param {*} bottomPos 下底面的位置 Vector3\r\n * @param {*} opts 选项\r\n * @returns \r\n */\r\nfunction tjs_createCylinderByTwoPoint(ctx, topRadius, bottomRadius, topPos, bottomPos, opts = {}) {\r\n    const { THREE } = ctx.imports;\r\n    const { color, opacity, receiveShadow, castShadow, radialSegments } = opts;\r\n\r\n    const t_topPos = new THREE.Vector3();\r\n    const t_bottomPos = new THREE.Vector3();\r\n\r\n    t_topPos.x = topPos[0];\r\n    t_topPos.y = topPos[1];\r\n    t_topPos.z = topPos[2];\r\n\r\n    t_bottomPos.x = bottomPos[0];\r\n    t_bottomPos.y = bottomPos[1];\r\n    t_bottomPos.z = bottomPos[2];\r\n\r\n\r\n\r\n    // 计算两点之间的方向向量\r\n    const direction = new THREE.Vector3().subVectors(t_topPos, t_bottomPos);\r\n    // 计算高度（两点之间的距离）\r\n    const height = direction.length();\r\n    // 创建一个标准的圆柱体\r\n    const cylinder = tjs_createCylinder(ctx, topRadius, bottomRadius, height, {\r\n        color: color || 0xffffff,\r\n        opacity: opacity,\r\n        receiveShadow: receiveShadow,\r\n        castShadow: castShadow,\r\n        radialSegments: radialSegments\r\n    });\r\n\r\n    // 计算圆柱体的中心点位置（两点的中点）\r\n    const center = new THREE.Vector3().addVectors(t_topPos, t_bottomPos).multiplyScalar(0.5);\r\n    cylinder.position.copy(center);\r\n\r\n    // 默认圆柱体的方向是沿着Y轴的，需要将其旋转到两点连线的方向\r\n    // 创建一个表示Y轴正方向的向量\r\n    const yAxis = new THREE.Vector3(0, 1, 0);\r\n    // 归一化方向向量\r\n    direction.normalize();\r\n    // 计算旋转轴（叉乘得到垂直于两个向量的第三个向量）\r\n    const rotationAxis = new THREE.Vector3().crossVectors(yAxis, direction);\r\n\r\n    // 如果两点在Y轴上，叉乘结果接近零向量，需要特殊处理\r\n    if (rotationAxis.length() < 0.001) {\r\n        // 如果方向向量和Y轴同向，不需要旋转\r\n        if (direction.y > 0) {\r\n            // 不做任何旋转\r\n        } else {\r\n            // 如果方向向量和Y轴反向，旋转180度\r\n            cylinder.rotateX(Math.PI);\r\n        }\r\n    } else {\r\n        // 归一化旋转轴\r\n        rotationAxis.normalize();\r\n        // 计算旋转角度（两向量的夹角）\r\n        const angle = Math.acos(yAxis.dot(direction));\r\n        // 应用旋转\r\n        cylinder.quaternion.setFromAxisAngle(rotationAxis, angle);\r\n    }\r\n\r\n    return cylinder;\r\n}\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/util/tjs-geometry-util.js?\n}");

/***/ }),

/***/ "./src/util/tjs-light-util.js":
/*!************************************!*\
  !*** ./src/util/tjs-light-util.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tjs_createBulb: () => (/* binding */ tjs_createBulb),\n/* harmony export */   tjs_createPointLightHelper: () => (/* binding */ tjs_createPointLightHelper)\n/* harmony export */ });\n/**\r\n * \r\n * @param {*} ctx \r\n * @param {*} pointLight \r\n */\r\nfunction tjs_createPointLightHelper(ctx, pointLight, opts = {}) {\r\n    const { THREE } = ctx.imports;\r\n    const pointLightHelper = new THREE.PointLightHelper(pointLight, 5);\r\n    return pointLightHelper;\r\n\r\n}\r\n\r\n/**\r\n * 创建灯泡\r\n * @param {*} ctx \r\n * @param {*} opts \r\n * @returns \r\n */\r\nfunction tjs_createBulb(ctx, opts = {}) {\r\n    const { THREE } = ctx.imports;\r\n    let { color, intensity, distance, decay } = opts;\r\n    if (color == null) color = 0xffee88;\r\n    if (intensity == null) intensity = 3;\r\n    if (distance == null) distance = 0;\r\n    if (decay == null) decay = 0.1;\r\n\r\n    const bulbGeometry = new THREE.SphereGeometry(3, 16, 8);\r\n    const bulbMat = new THREE.MeshStandardMaterial({\r\n        emissive: 0xffffee,\r\n        emissiveIntensity: 1,\r\n        color: 0x000000\r\n    });\r\n    const mesh = new THREE.Mesh(bulbGeometry, bulbMat);\r\n    mesh.name = 'bulbMesh';\r\n\r\n    const bulbLight = new THREE.PointLight(color, intensity, distance, decay);\r\n    bulbLight.name = 'bulbLight';\r\n    bulbLight.castShadow = true;\r\n    // 设置阴影精度\r\n    bulbLight.shadow.mapSize.width = 1024 * 2;\r\n    bulbLight.shadow.mapSize.height = 1024 * 2;\r\n    // 设置阴影范围（关键）\r\n    bulbLight.shadow.camera.near = 0.5;\r\n    bulbLight.shadow.camera.far = 100000;\r\n    \r\n    mesh.add(bulbLight);\r\n\r\n    return mesh;\r\n}\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/util/tjs-light-util.js?\n}");

/***/ }),

/***/ "./src/util/web-util.js":
/*!******************************!*\
  !*** ./src/util/web-util.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearHTML: () => (/* binding */ clearHTML),\n/* harmony export */   isMobile: () => (/* binding */ isMobile),\n/* harmony export */   queryString: () => (/* binding */ queryString),\n/* harmony export */   touchEvent: () => (/* binding */ touchEvent)\n/* harmony export */ });\n// 获取url参数\r\nfunction queryString(name, _window) {\r\n    let t_window = null;\r\n    if (_window != null) {\r\n        t_window = _window;\r\n    } else {\r\n        t_window = window;\r\n    }\r\n\r\n    const reg = new RegExp(\"(^|&)\" + name + \"=([^&]*)(&|$)\", \"i\");\r\n    const r = t_window.location.search.substr(1).match(reg);\r\n    if (r != null)\r\n        return r[2]; /*unescape(r[2]);*/\r\n    return '';\r\n}\r\n\r\n// 清理html内容\r\nfunction clearHTML(val) {\r\n    if (val != null) {\r\n        const html = val.replace(/(<!--\\s*(((?!<\\/script>)(?:.|[\\r\\n]))*)\\s*-->)|(<script>\\s*(((?!<\\/script>)(?:.|[\\r\\n]))*)\\s*<\\/script>)|(<style>\\s*(((?!<\\/style>)(?:.|[\\r\\n]))*)\\s*<\\/style>)|(<[^>]*>)|(&[^;]+;)/ig, '');\r\n        return html;\r\n    }\r\n    return val;\r\n}\r\n\r\n// 是否手机端\r\nfunction isMobile() {\r\n    if (navigator.userAgent.toLowerCase().indexOf(\"mobile\") !== -1) {\r\n        return true;\r\n    }\r\n    return false;\r\n}\r\n\r\n\r\n// 手动触发事件\r\nfunction touchEvent(tgt, eventName, win) {\r\n    if (win == null) win = window;\r\n    const ev = document.createEvent('MouseEvents');\r\n    ev.initMouseEvent(eventName, true, true, win)\r\n    tgt.dispatchEvent(ev);\r\n}\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/util/web-util.js?\n}");

/***/ }),

/***/ "./src/view-model/PropBarViewModel.js":
/*!********************************************!*\
  !*** ./src/view-model/PropBarViewModel.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PropBarViewModel: () => (/* binding */ PropBarViewModel)\n/* harmony export */ });\nclass PropBarViewModel {\r\n    constructor(ctx) {\r\n        this.ctx = ctx;\r\n\r\n        this._vue;\r\n    }\r\n\r\n    async build() {\r\n        const { ctx } = this;\r\n        const vmSelf = this;\r\n        this._vue = new Vue({\r\n            el: '#propBar',\r\n            data: function () {\r\n                const selectedProps = {};\r\n                for (const type in ctx.objectTypes) {\r\n                    const objectType = ctx.objectTypes[type];\r\n                    const props = objectType.getProps();\r\n                    for (const prop of props) {\r\n                        selectedProps[prop.name] = null;\r\n                    }\r\n                }\r\n\r\n                const playerProps = {};\r\n                {\r\n                    const props = vmSelf.getPlayerPropDesignList();\r\n                    for (const prop of props) {\r\n                        playerProps[prop.name] = null;\r\n                    }\r\n                }\r\n\r\n                return {\r\n                    // 激活Tab：selected | designer | player | world\r\n                    activeTabName: 'selected',\r\n                    focusPropName: null,\r\n\r\n                    // selected\r\n                    selectedObject: null,\r\n                    selectedProps: selectedProps,\r\n                    selectedPropDesignList: [],\r\n\r\n                    // player\r\n                    playerProps: playerProps,\r\n                    playerPropDesignList: [],\r\n                };\r\n            },\r\n            methods: {\r\n                switchTab(tabName) {\r\n                    this.activeTabName = tabName;\r\n\r\n                    if (tabName === 'player') {\r\n                        this.updatePlayerTab();\r\n                    }\r\n                },\r\n\r\n                onUserSelectObject(e) {\r\n                    this.selectedObject = e.target;\r\n                    this.selectedProps.id = this.selectedObject.getId();\r\n\r\n                    if (ctx.objectTypes[this.selectedObject.constructor.name].getProps) {\r\n                        const props = ctx.objectTypes[this.selectedObject.constructor.name].getProps();\r\n                        this.selectedPropDesignList = props;\r\n\r\n                        for (const prop of props) {\r\n                            let t = this.selectedObject.getPropValue(prop.name);\r\n                            if (t === undefined) {\r\n                                t = prop.default;\r\n                            }\r\n                            this.selectedProps[prop.name] = t;\r\n                        }\r\n                    }\r\n                },\r\n                onUserCancelSelectObject(e) {\r\n                    this.selectedObject = null;\r\n                    for (const prop in this.selectedProps) {\r\n                        this.selectedProps[prop.name] = null;\r\n                    }\r\n                    this.selectedPropDesignList = [];\r\n                },\r\n                async saveProject() {\r\n                    const loading = this.$loading({ lock: true, text: '处理中' });\r\n                    await ctx.projectStorageService.save();\r\n                    loading.close();\r\n                },\r\n                isFocusProp(name) {\r\n                    return this.focusPropName === name;\r\n                },\r\n                handleFocusProp(name) {\r\n                    if (this.focusPropName === name) {\r\n                        this.focusPropName = null;\r\n                    }\r\n                    else {\r\n                        this.focusPropName = name;\r\n                    }\r\n                },\r\n\r\n                updateSelectedTab() {\r\n                    if (this.selectedObject) {\r\n                        if (ctx.objectTypes[this.selectedObject.constructor.name].getProps) {\r\n                            const props = ctx.objectTypes[this.selectedObject.constructor.name].getProps();\r\n\r\n                            for (const prop of props) {\r\n                                let t = this.selectedObject.getPropValue(prop.name);\r\n                                if (t === undefined) {\r\n                                    t = prop.default;\r\n                                }\r\n                                this.selectedProps[prop.name] = t;\r\n                            }\r\n                        }\r\n                    }\r\n                },\r\n                handleSelectedPropChange(name, e) {\r\n                    this.selectedObject.setPropValue(name, e);\r\n                    const gucaeAbility = ctx.cgWorld.getAbility('get_user_change_any_event')\r\n                    gucaeAbility.touchEvent('user-change-object', {});\r\n                },\r\n                handleSelectedDelete(e) {\r\n                    e.preventDefault(); // 阻止发生默认行为\r\n                    e.stopPropagation(); // 阻止事件冒泡到父元素\r\n\r\n                    var self = this;\r\n                    if (this.selectedProps.id == null || this.selectedProps.id.length === 0) {\r\n                        // alert('请先选中对象');\r\n                        self.$alert('请先选中对象', { type: 'error' });\r\n                        return;\r\n                    }\r\n\r\n                    self.$confirm('确定要删除选中的对象吗？', '确认提示', { type: 'warning' }).then(() => {\r\n                        // 点击确认\r\n                        ctx.cgWorld.removeObjectById(this.selectedProps.id);\r\n                        const gucaeAbility = ctx.cgWorld.getAbility('get_user_change_any_event')\r\n                        gucaeAbility.touchEvent('user-delete-object', {});\r\n                    }).catch(() => {\r\n                        // 点击取消\r\n                    });\r\n                },\r\n                handleSelectedMouseWheel(name, e) {\r\n                    if (this.focusPropName === name) {\r\n                        e.preventDefault(); // 阻止发生默认行为\r\n                        e.stopPropagation(); // 阻止事件冒泡到父元素\r\n\r\n                        const focusPropDesign = this.selectedPropDesignList.find(n => n.name === this.focusPropName);\r\n                        if (focusPropDesign && focusPropDesign.type === 'Number') {\r\n                            let eachStepAmount = 1;\r\n                            const findPropDesign = this.selectedPropDesignList.find(item => item.name == name);\r\n                            if (findPropDesign && findPropDesign.step != null) {\r\n                                eachStepAmount = findPropDesign.step;\r\n                            }\r\n\r\n                            if (e.deltaY < 0) {\r\n                                let t = this.selectedProps[name] ?? 0;\r\n                                this.$set(this.selectedProps, name, parseFloat((t + eachStepAmount).toFixed(2)));\r\n\r\n                            } else {\r\n                                let t = this.selectedProps[name] ?? 0;\r\n                                this.$set(this.selectedProps, name, parseFloat((t - eachStepAmount).toFixed(2)));\r\n                            }\r\n                            this.selectedObject.setPropValue(name, this.selectedProps[name]);\r\n                            const gucaeAbility = ctx.cgWorld.getAbility('get_user_change_any_event')\r\n                            gucaeAbility.touchEvent('user-change-object', {});\r\n                        }\r\n                    }\r\n                },\r\n                handleSelectedKeydown(e) {\r\n                    e.stopPropagation(); // 阻止事件冒泡到父元素\r\n                },\r\n\r\n                updatePlayerTab() {\r\n                    this.playerProps = {\r\n                        showCCI: ctx.data.player.showCCI,\r\n                    };\r\n                    this.playerPropDesignList = vmSelf.getPlayerPropDesignList();\r\n                },\r\n                handlePlayerPropChange(name, e) {\r\n                    if (name === 'showCCI') {\r\n                        vmSelf.ctx.data.player.showCenterIndicator = e;\r\n                        const gucaeAbility = ctx.cgWorld.getAbility('get_user_change_any_event')\r\n                        gucaeAbility.touchEvent('user-change-object', {});\r\n                    }\r\n                },\r\n                handlePlayerMouseWheel(name, e) {\r\n                    if (this.focusPropName === name) {\r\n                        e.preventDefault(); // 阻止发生默认行为\r\n                        e.stopPropagation(); // 阻止事件冒泡到父元素\r\n\r\n                        const focusPropDesign = this.playerPropDesignList.find(n => n.name === this.focusPropName);\r\n                        if (focusPropDesign && focusPropDesign.type === 'Number') {\r\n                            let eachStepAmount = 1;\r\n\r\n                            if (e.deltaY < 0) {\r\n                                let t = this.playerProps[name] ?? 0;\r\n                                this.$set(this.playerProps, name, parseFloat((t + eachStepAmount).toFixed(2)));\r\n\r\n                            } else {\r\n                                let t = this.playerProps[name] ?? 0;\r\n                                this.$set(this.playerProps, name, parseFloat((t - eachStepAmount).toFixed(2)));\r\n                            }\r\n                            if (name === 'xxx') {\r\n                            }\r\n\r\n                            const gucaeAbility = ctx.cgWorld.getAbility('get_user_change_any_event')\r\n                            gucaeAbility.touchEvent('user-change-object', {});\r\n                        }\r\n                    }\r\n                }\r\n            },\r\n            created: function () {\r\n            },\r\n            mounted: function () {\r\n                var self = this;\r\n\r\n                // 监听用户选中对象事件\r\n                const gucaeAbility = ctx.cgWorld.getAbility('get_user_change_any_event');\r\n                if (gucaeAbility) {\r\n                    gucaeAbility.addEventListener('pb_vm_0', ['user-select-object'], (e) => {\r\n                        self.onUserSelectObject(e);\r\n                    });\r\n                    gucaeAbility.addEventListener('pb_vm_1', ['user-cancel-select-object'], (e) => {\r\n                        self.onUserCancelSelectObject(e);\r\n                    });\r\n                }\r\n\r\n                const selectedObject = ctx.cgWorld.getSelectedObject();\r\n                if (selectedObject) {\r\n                    self.onUserSelectObject({\r\n                        target: selectedObject,\r\n                    });\r\n                }\r\n\r\n                this.$nextTick(function () {\r\n                });\r\n            }\r\n        });\r\n    }\r\n\r\n    showMessage(text) {\r\n        if (this._vue) {\r\n            this._vue.$message({ message: text, type: 'success' });\r\n        }\r\n    }\r\n\r\n    updateSelectedTab() {\r\n        if (this._vue) {\r\n            this._vue.updateSelectedTab();\r\n        }\r\n    }\r\n\r\n    updatePlayerTab() {\r\n        if (this._vue) {\r\n            this._vue.updatePlayerTab();\r\n        }\r\n    }\r\n\r\n    getPlayerPropDesignList() {\r\n        return [\r\n            { label: '中心坐标指示器', title: '中心坐标指示器（CCI）', name: 'showCCI', type: 'Boolean', default: false },\r\n        ]\r\n    }\r\n}\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/view-model/PropBarViewModel.js?\n}");

/***/ }),

/***/ "./src/view-model/ToolBarViewModel.js":
/*!********************************************!*\
  !*** ./src/view-model/ToolBarViewModel.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToolBarViewModel: () => (/* binding */ ToolBarViewModel)\n/* harmony export */ });\n/* harmony import */ var _model_CGObjectModel__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../model/CGObjectModel */ \"./src/model/CGObjectModel.js\");\n/* harmony import */ var _util_tjs_camera_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../util/tjs-camera-util */ \"./src/util/tjs-camera-util.js\");\n\r\n\r\n\r\nclass ToolBarViewModel {\r\n    constructor(ctx) {\r\n        this.ctx = ctx;\r\n\r\n        this._vue;\r\n    }\r\n\r\n    async build() {\r\n        const { ctx } = this;\r\n        const vmSelf = this;\r\n        this._vue = new Vue({\r\n            el: '#toolBar',\r\n            data: function () {\r\n                return {\r\n                    models: [],\r\n                    activeTabName: 'models',\r\n                };\r\n            },\r\n            methods: {\r\n                switchTab(tabName) {\r\n                    this.activeTabName = tabName;\r\n                },\r\n                async loadData() {\r\n                    const models = await vmSelf.ctx.modelsStorageMapper.loadList();\r\n                    this.models = models;\r\n\r\n                    // 测试\r\n                    // for (let i = 0; i < 100; i++) {\r\n                    //     this.models.push(models[0])\r\n                    // }\r\n                },\r\n                getModelIcon(item) {\r\n                    if (item.icon == null || item.icon.length === 0) {\r\n                        return './assets/img/model.svg';\r\n                    }\r\n                    \r\n                    if (item.type === 'import-model') {\r\n                        return `./data/import-model/${item.id}${item.icon}`;\r\n                    }\r\n                    else {\r\n                        return `./data/custom-model/${item.id}${item.icon}`;\r\n                    }\r\n                },\r\n                getModelType(item) {\r\n                    if (item.type === 'import-model') {\r\n                        return '导入';\r\n                    }\r\n                    else {\r\n                        return '内置';\r\n                    }\r\n                },\r\n                getTags(item) {\r\n                    return item.tags.toString().replace(/[,]/g, '，');\r\n                },\r\n                async handleItemClick(item) {\r\n                    // 获取镜头朝向的交叉点\r\n                    const pos = (0,_util_tjs_camera_util__WEBPACK_IMPORTED_MODULE_1__.tjs_getCameraToXZPos)(ctx, ctx.camera);\r\n                    // 获取网格中心点\r\n                    const pos1 = self.ctx.utils.getDesignGroundGridRealPosByRealPos(pos);\r\n                    console.log('添加模型', item, pos1);\r\n\r\n                    if (item.type === 'import-model') {\r\n                        await ctx.cgWorld.addObjectAndBuild(new _model_CGObjectModel__WEBPACK_IMPORTED_MODULE_0__.CGObjectModel(ctx, {\r\n                            tags: item.tags,\r\n                            // posUnit: [0, 0, 0],\r\n                            pos: pos1,\r\n                            modelLoader: {\r\n                                url: `./data/import-model/${item.id}`\r\n                            },\r\n                            collidableObject: {},\r\n                            ...item.data,\r\n                        }));\r\n                    }\r\n                    else if (item.type.endsWith('-object-build')) {\r\n                        let typeName = item.type.replace('-object-build', '');\r\n                        await ctx.cgWorld.addObjectAndBuild(new ctx.objectTypes[typeName](ctx, {\r\n                            tags: item.tags,\r\n                            ...item.data,\r\n                            pos: pos1,\r\n                        }));\r\n                    }\r\n\r\n                    ctx.cgWorld.getAbility('get_user_change_any_event').touchEvent('user-add-object', {});\r\n                },\r\n            },\r\n            created: function () {\r\n            },\r\n            mounted: async function () {\r\n                var self = this;\r\n                await self.loadData();\r\n                this.$nextTick(function () {\r\n                });\r\n            }\r\n        });\r\n    }\r\n}\n\n//# sourceURL=webpack://cs-hotspring-tpl-3d/./src/view-model/ToolBarViewModel.js?\n}");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/global */
/******/ 	(() => {
/******/ 		__webpack_require__.g = (function() {
/******/ 			if (typeof globalThis === 'object') return globalThis;
/******/ 			try {
/******/ 				return this || new Function('return this')();
/******/ 			} catch (e) {
/******/ 				if (typeof window === 'object') return window;
/******/ 			}
/******/ 		})();
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("./src/main-start.js");
/******/ 	
/******/ })()
;