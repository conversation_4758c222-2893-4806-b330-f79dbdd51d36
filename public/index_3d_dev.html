<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title></title>
    <meta name='viewport'
        content='width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no' />
    <link href="" rel="stylesheet">
    <style>
        body {
            overflow: hidden;
            margin: 0;
            padding: 0;
            background-color: #000;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: Arial, sans-serif;
        }

        #gameCanvas {
            border: 1px solid #fff;
            background-color: #333;
        }
    </style>
    <link rel="stylesheet" href="./assets/css/dev-ui.css">
    <script src="./src/main-start.js"></script>
    <script src="./lib/vue/vue.2.6.14.min.js"></script>
    <!-- Element UI -->
    <link rel="stylesheet" href="./lib/element-ui-2/theme-chalk/index.css">
    <script src="./lib/element-ui-2/index.js"></script>
</head>

<body>
    <canvas id="gameCanvas" width="800" height="600"></canvas>
    <div id="toolBar" class="tool-bar">
        <div class="bar-title">工具栏</div>
        <!-- Tab切换 -->
        <div class="tab-container">
            <div class="tab-header">
                <div class="tab-item" :class="{active: activeTabName === 'models'}" @click="switchTab('models')">模型库
                </div>
                <div class="tab-item" :class="{active: activeTabName === 'spaces'}" @click="switchTab('spaces')">空间库
                </div>
            </div>
            <div class="tab-body">
                <div class="models" v-if="activeTabName === 'models'">
                    <template v-for="model in models">
                        <div class="model-item" @click="handleItemClick(model)">
                            <div class="split-0"></div>
                            <div class="model-icon">
                                <img :src="getModelIcon(model)" />
                            </div>
                            <div class="split-1"></div>
                            <div class="model-info">
                                <div>
                                    <span class="model-name">{{model.name}}</span>
                                    <span class="model-type">（{{getModelType(model)}}）</span>
                                </div>
                                <div>
                                    <span class="model-tags">{{getTags(model)}}</span>
                                </div>
                            </div>
                            <div class="split-2"></div>
                        </div>
                    </template>
                </div>
                <div class="spaces" v-if="activeTabName === 'spaces'">
                    <div style="padding: 20px; text-align: center; color: #a0a0a0; font-size: 14px;">
                        空间库功能开发中...
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="propBar" class="prop-bar">
        <div class="bar-title">属性栏</div>
        <!-- Tab切换 -->
        <div class="tab-container">
            <div class="tab-header">
                <div class="tab-item" :class="{active: activeTabName === 'selected'}" @click="switchTab('selected')">选中</div>
                <div class="tab-item" :class="{active: activeTabName === 'designer'}" @click="switchTab('designer')">设计师</div>
                <div class="tab-item" :class="{active: activeTabName === 'player'}" @click="switchTab('player')">玩家</div>
                <div class="tab-item" :class="{active: activeTabName === 'world'}" @click="switchTab('world')">世界</div>
            </div>
            <div class="tab-body">
                <!-- 选中对象面板 -->
                <div class="selected" v-if="activeTabName === 'selected'">
                    <!-- 属性 -->
                    <div class="props">
                        <template v-for="prop in selectedPropDesignList">
                            <div :class="{ 'prop-item': true, 'focus-prop-item': isFocusProp(prop.name) }" @mousewheel="handleSelectedMouseWheel(prop.name, $event)">
                                <div class="prop-label" :title="prop.title" @click="handleFocusProp(prop.name)">
                                    <div class="inner-text">{{prop.label}}</div>
                                </div>
                                <div class="prop-value">
                                    <template v-if="prop.type === 'Number'">
                                        <el-input-number size="small" v-model="selectedProps[prop.name]"
                                            :readonly="prop.readonly" :controls="false"
                                            @change="handleSelectedPropChange(prop.name, $event)"
                                             />
                                    </template>
                                    <template v-else>
                                        <el-input size="small" v-model="selectedProps[prop.name]"
                                            :readonly="prop.readonly" @change="handleSelectedPropChange(prop.name, $event)" 
                                            @keydown.native="handleSelectedKeydown" />
                                    </template>
                                </div>
                            </div>
                        </template>
                    </div>
                    <div class="buttons">
                        <button @click="handleSelectedDelete" v-if="selectedObject != null">删除选中对象</button>
                    </div>
                </div>
                <!-- 设计师面板 -->
                <div class="designer" v-if="activeTabName === 'designer'">
                </div>
                <!-- 玩家面板 -->
                <div class="player" v-if="activeTabName === 'player'">
                    <!-- 属性 -->
                    <div class="props">
                        <template v-for="prop in playerPropDesignList">
                            <div :class="{ 'prop-item': true, 'focus-prop-item': isFocusProp(prop.name) }" @mousewheel="handlePlayerMouseWheel(prop.name, $event)">
                                <div class="prop-label" :title="prop.title" @click="handleFocusProp(prop.name)">
                                    <div class="inner-text">{{prop.label}}</div>
                                </div>
                                <div class="prop-value">
                                    <template v-if="prop.type === 'Number'">
                                        <el-input-number size="small" v-model="playerProps[prop.name]"
                                            :readonly="prop.readonly" :controls="false"
                                            @change="handlePlayerPropChange(prop.name, $event)"
                                             />
                                    </template>
                                    <template v-if="prop.type === 'Boolean'">
                                        <el-checkbox size="small" v-model="playerProps[prop.name]"
                                            :readonly="prop.readonly"
                                            @change="handlePlayerPropChange(prop.name, $event)" />
                                    </template>
                                    <template v-else>
                                        <el-input size="small" v-model="playerProps[prop.name]"
                                            :readonly="prop.readonly" @change="handlePlayerPropChange(prop.name, $event)" />
                                    </template>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
                <!-- 世界面板 -->
                <div class="world" v-if="activeTabName === 'world'">
                </div>
            </div>
            <div>
                <el-button type="success" @click="saveProject" style="width:100%;">保存项目</el-button>
            </div>
        </div>
    </div>
    <script type="importmap">
        {
            "imports": {
                "three": "./lib/threejs/build/three.module.js",
                "three/webgpu": "./lib/threejs/build/three.webgpu.js",
                "three/tsl": "./lib/threejs/build/three.tsl.js",
                "three/addons/CSS2DRenderer": "./lib/threejs/examples/jsm/renderers/CSS2DRenderer.js",
                "three/addons/FBXLoader": "./lib/threejs/examples/jsm/loaders/FBXLoader.js",
                "three/addons/OrbitControls": "./lib/threejs/examples/jsm/controls/OrbitControls.js",
                "three/addons/Sky": "./lib/threejs/examples/jsm/objects/Sky.js",
                "cannon-es": "./lib/cannon-es/cannon-es.js",
                "cannon-es-debugger": "./lib/cannon-es/cannon-es-debugger.js"
            }
        }
    </script>
    <script>
        /*
        */
    </script>
    <script type="module">
        import * as THREE from 'three';
        import { CSS2DRenderer } from "three/addons/CSS2DRenderer";
        import { CSS2DObject } from "three/addons/CSS2DRenderer";
        import { OrbitControls } from "three/addons/OrbitControls";
        import { FBXLoader } from "three/addons/FBXLoader";
        import { Sky } from "three/addons/Sky";
        // import {CSS3DRenderer} from "./lib/threejs/examples/jsm/renderers/CSS3DRenderer.js";

        import * as CANNON from 'cannon-es';
        import CannonDebugger from 'cannon-es-debugger';

        // import { start } from "../src/main-start.js";

        start({
            THREE, CSS2DRenderer, CSS2DObject, FBXLoader,
            OrbitControls,
            Sky,
            CANNON, CannonDebugger,
        }, {
            gameType: '3d',
            dev: true,
            editor: true,
            canAutoSaveProject: true,
        })
    </script>
</body>

</html>