/* 
使用命令编译：lessc dev-ui.less dev-ui.css --plugin=less-plugin-clean-css
npm i -g less
npm i -g less-plugin-clean-css
*/

.el-popup-parent--hidden {
    padding-right: 0 !important;
}

// 工具栏（左侧悬浮）
.tool-bar {
    position: absolute;
    top: 10px;
    left: 10px;
    bottom: 10px;
    width: 330px;
    background: linear-gradient(135deg, rgba(45, 45, 45, 0.95), rgba(30, 30, 30, 0.95));
    backdrop-filter: blur(10px);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: all 0.3s ease;
    opacity: 0.9;

    &:hover {
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
        transform: translateY(-2px);
    }

    .bar-title {
        color: #ffffff;
        font-size: 18px;
        font-weight: 600;
        text-align: center;
        line-height: 60px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        margin: 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    // Tab切换容器
    .tab-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .tab-header {
            display: flex;
            background: rgba(255, 255, 255, 0.05);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);

            .tab-item {
                flex: 1;
                padding: 12px 16px;
                text-align: center;
                color: #a0a0a0;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s ease;
                position: relative;
                background: transparent;
                border: none;
                outline: none;

                &:hover {
                    color: #ffffff;
                    background: rgba(255, 255, 255, 0.08);
                }

                &.active {
                    color: #ffffff;
                    background: linear-gradient(135deg, rgba(102, 126, 234, 0.3), rgba(118, 75, 162, 0.3));

                    &::after {
                        content: '';
                        position: absolute;
                        bottom: 0;
                        left: 50%;
                        transform: translateX(-50%);
                        width: 60%;
                        height: 2px;
                        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                        border-radius: 1px;
                    }
                }

                &:not(:last-child) {
                    border-right: 1px solid rgba(255, 255, 255, 0.1);
                }
            }
        }

        .tab-body {
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;

            .models,
            .spaces {
                flex: 1;
                overflow-y: auto;
                height: 0;
            }
        }
    }

    // 模型库和空间库通用样式
    .models,
    .spaces {
        padding: 8px 8px 0 8px;

        // 自定义滚动条
        &::-webkit-scrollbar {
            width: 6px;
        }

        &::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            transition: background 0.3s ease;

            &:hover {
                background: rgba(255, 255, 255, 0.5);
            }
        }

        // 模型列表项
        .model-item {
            display: flex;
            flex-direction: row;
            align-items: center;
            padding: 12px;
            margin: 0 0 10px 0;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;

            &:hover {
                background: rgba(255, 255, 255, 0.1);
                border-color: rgba(255, 255, 255, 0.2);
                transform: translateX(4px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }

            &:active {
                transform: translateX(2px) scale(0.98);
            }

            .split-0 {
                width: 8px;
                height: auto;
            }

            .model-icon {
                border-radius: 6px;
                overflow: hidden;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

                img {
                    height: 50px;
                    width: 50px;
                    object-fit: cover;
                    transition: transform 0.3s ease;
                }

                &:hover img {
                    transform: scale(1.1);
                }
            }

            .split-1 {
                width: 12px;
                height: auto;
            }

            .model-info {
                flex: 1;
                display: flex;
                flex-direction: column;
                gap: 4px;

                .model-name {
                    flex: 1;
                    color: #ffffff;
                    font-weight: 500;
                    font-size: 14px;
                    line-height: 1.4;
                }

                .model-type {
                    color: #a0a0a0;
                    font-size: 12px;
                    font-weight: 400;
                    opacity: 0.8;
                }

                .model-tags {
                    color: #a0a0a0;
                    font-size: 12px;
                    font-weight: 400;
                    opacity: 0.8;

                }
            }

            .split-2 {
                width: 8px;
                height: auto;
            }
        }
    }
}

// 属性栏（右侧悬浮）
.prop-bar {
    position: absolute;
    top: 10px;
    right: 10px;
    bottom: 10px;
    width: 330px;
    background: linear-gradient(135deg, rgba(45, 45, 45, 0.95), rgba(30, 30, 30, 0.95));
    backdrop-filter: blur(10px);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: all 0.3s ease;
    opacity: 0.9;

    &:hover {
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
        transform: translateY(-2px);
    }

    .bar-title {
        color: #ffffff;
        font-size: 18px;
        font-weight: 600;
        text-align: center;
        line-height: 60px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        margin: 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    // Tab切换容器
    .tab-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .tab-header {
            display: flex;
            background: rgba(255, 255, 255, 0.05);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);

            .tab-item {
                flex: 1;
                padding: 10px 12px;
                text-align: center;
                color: #a0a0a0;
                font-size: 13px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s ease;
                position: relative;
                background: transparent;
                border: none;
                outline: none;

                &:hover {
                    color: #ffffff;
                    background: rgba(255, 255, 255, 0.08);
                }

                &.active {
                    color: #ffffff;
                    background: linear-gradient(135deg, rgba(102, 126, 234, 0.3), rgba(118, 75, 162, 0.3));

                    &::after {
                        content: '';
                        position: absolute;
                        bottom: 0;
                        left: 50%;
                        transform: translateX(-50%);
                        width: 60%;
                        height: 2px;
                        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                        border-radius: 1px;
                    }
                }

                &:not(:last-child) {
                    border-right: 1px solid rgba(255, 255, 255, 0.1);
                }
            }
        }

        .tab-body {
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            padding: 0;

            .selected,
            .designer,
            .player,
            .world {
                flex: 1;
                overflow-y: auto;
                height: 0;
                color: #ffffff;
                font-size: 14px;
            }

            // 选中对象面板样式
            .selected,
            .designer,
            .player,
            .world {
                display: flex;
                flex-direction: column;

                .props {
                    flex: 1;
                    height: 0;
                    padding: 8px;
                    overflow-y: auto;

                    // 自定义滚动条
                    &::-webkit-scrollbar {
                        width: 6px;
                    }

                    &::-webkit-scrollbar-track {
                        background: rgba(255, 255, 255, 0.1);
                        border-radius: 3px;
                    }

                    &::-webkit-scrollbar-thumb {
                        background: rgba(255, 255, 255, 0.3);
                        border-radius: 3px;
                        transition: background 0.3s ease;

                        &:hover {
                            background: rgba(255, 255, 255, 0.5);
                        }
                    }

                    .prop-item {
                        display: flex;
                        align-items: stretch;
                        margin-bottom: 8px;
                        padding: 6px;
                        background: rgba(255, 255, 255, 0.05);
                        border-radius: 8px;
                        border: 1px solid rgba(255, 255, 255, 0.1);
                        transition: all 0.3s ease;

                        &:hover {
                            background: rgba(255, 255, 255, 0.08);
                            // border-color: rgba(102, 126, 234, 0.5);
                            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
                        }

                        .prop-label {
                            display: flex;
                            align-items: center;
                            width: 90px;
                            // height: 100%;
                            color: #a0a0a0;
                            font-size: 12px;
                            font-weight: 500;
                            text-transform: uppercase;
                            letter-spacing: 0.5px;

                            .inner-text {
                                width: 80px;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                            }
                        }

                        .prop-value {
                            flex: 1;

                            input[type="text"],
                            input[type="number"] {
                                width: 100%;
                                background: transparent;
                                border: 0;
                                border-radius: 6px;
                                color: #ffffff;
                                font-size: 14px;
                                outline: none;
                                transition: all 0.3s ease;

                                // &:focus {
                                //     border-color: rgba(102, 126, 234, 0.8);
                                //     box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
                                //     background: rgba(0, 0, 0, 0.5);
                                // }

                                &[readonly] {
                                    cursor: not-allowed;
                                    opacity: 0.7;
                                }
                            }
                        }
                    }

                    .focus-prop-item {
                        background: rgba(255, 255, 255, 0.08);
                        border-color: rgba(165, 60, 226, 0.5);
                        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
                    }
                }

                .buttons {
                    margin-top: 24px;
                    display: flex;
                    gap: 12px;

                    button {
                        flex: 1;
                        padding: 12px 24px;
                        background: linear-gradient(135deg, #e74c3c, #c0392b);
                        border: none;
                        border-radius: 8px;
                        color: #ffffff;
                        font-size: 14px;
                        font-weight: 500;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);

                        &:hover {
                            transform: translateY(-2px);
                            box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
                            background: linear-gradient(135deg, #ec5f4f, #d44637);
                        }

                        &:active {
                            transform: translateY(0);
                            box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
                        }
                    }
                }
            }
        }
    }
}