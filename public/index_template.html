<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title></title>
    <meta name='viewport'
        content='width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no' />
    <link href="" rel="stylesheet">
    <style>
        body {
            overflow: hidden;
            margin: 0;
            padding: 0;
            background-color: #000;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: Arial, sans-serif;
        }

        #gameCanvas {
            border: 1px solid #fff;
            background-color: #333;
        }
    </style>
</head>

<body>
    <canvas id="gameCanvas" width="800" height="600"></canvas>
    <script type="importmap">
    {
      "imports": {
        "three": "./lib/threejs/build/three.module.js",
        "three/addons/CSS2DRenderer": "./lib/threejs/examples/jsm/renderers/CSS2DRenderer.js",
        "three/addons/": "./lib/threejs/examples/jsm/"
      }
    }
</script>
    <!-- <script type="module" src="./code/main.js"></script> -->
    <script type="module">
        // *** 详细参考看 my-demo-3 ***
        // import VortexToCenterAnimation from './code/animation/VortexToCenterAnimation.js';
        import * as THREE from 'three';
        import { CSS2DRenderer } from "./lib/threejs/examples/jsm/renderers/CSS2DRenderer.js";
        import { CSS2DObject } from "three/addons/CSS2DRenderer";
        // import {CSS3DRenderer} from "./lib/threejs/examples/jsm/renderers/CSS3DRenderer.js";
        import { FBXLoader } from "./lib/threejs/examples/jsm/loaders/FBXLoader.js";
        import { DevViewportService } from "./src/service/DevViewportService.js";
        import { UserControllerService } from "./src/service/UserControllerService.js";

        // 上下文
        const ctx = {
            // 启用开发者视口
            enableDevViewport: true,

            // 默认场景
            scene: null,
            // 默认相机（游戏正式运行时主相机，非开发者视口的相机）
            camera: null,
            renderer: null,
            css2dRenderer: null,

            imports: {
                THREE, CSS2DObject
            }
        };

        // 动画
        function animate() {
            requestAnimationFrame(animate);
            if (ctx.devViewportService) {
                ctx.devViewportService.animate();
            }
            ctx.renderer.render(ctx.scene, ctx.camera);
            ctx.css2dRenderer.render(ctx.scene, ctx.camera);
        }

        // 初始化图形引擎
        async function initGraphicsEngine(ctx) {
            const scene = new THREE.Scene();

            // const camera = new THREE.OrthographicCamera(-400, 400, 300, -300, 1, 500); // 创建正交相机（2d）
            const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000); // 创建透视相机（3d）
            camera.position.set(0, 300, 500); // 调整相机高度
            camera.lookAt(0, 0, 0);

            const renderer = new THREE.WebGLRenderer({ canvas: document.getElementById('gameCanvas') });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;

            const css2dRenderer = new CSS2DRenderer();
            css2dRenderer.setSize(window.innerWidth, window.innerHeight);
            css2dRenderer.domElement.style.position = 'absolute';
            css2dRenderer.domElement.style.zIndex = "2";
            document.body.appendChild(css2dRenderer.domElement);

            ctx.scene = scene;
            ctx.camera = camera;
            ctx.renderer = renderer;
            ctx.css2dRenderer = css2dRenderer;

            // 启用用户控制器
            ctx.userControllerService = new UserControllerService(ctx, {});
            ctx.userControllerService.start();

            // 启用开发者视口服务
            if (ctx.enableDevViewport) {
                console.log("启用开发者视口服务");
                ctx.devViewportService = new DevViewportService(ctx, {
                    defCamera: ctx.camera,
                    devCameraPos: { x: 0, y: 0, z: 300 },
                });
                ctx.devViewportService.start();
            }

            // 自动适应窗口大小
            window.addEventListener('resize', () => {
                ctx.camera.aspect = window.innerWidth / window.innerHeight;
                ctx.camera.updateProjectionMatrix();
                ctx.renderer.setSize(window.innerWidth, window.innerHeight);
                ctx.css2dRenderer.setSize(window.innerWidth, window.innerHeight);
            });
        }

        // 加载光源
        async function loadLightSource(ctx) {
            // 环境光源
            ctx.ambientLight = new THREE.AmbientLight(0xffffff, 0.1);
            ctx.scene.add(ctx.ambientLight);

            // 方向光源 - 启用以提供更好的3D效果
            ctx.directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
            ctx.directionalLight.position.set(150, 200, 150);
            ctx.directionalLight.castShadow = true;
            ctx.directionalLight.shadow.mapSize.width = 2048;
            ctx.directionalLight.shadow.mapSize.height = 2048;
            ctx.directionalLight.shadow.camera.near = 0.5;
            ctx.directionalLight.shadow.camera.far = 500;
            ctx.directionalLight.shadow.camera.left = -200;
            ctx.directionalLight.shadow.camera.right = 200;
            ctx.directionalLight.shadow.camera.top = 200;
            ctx.directionalLight.shadow.camera.bottom = -200;
            ctx.scene.add(ctx.directionalLight);

            if (ctx.enableDevViewport) {
                // 添加方向光源辅助对象
                const dirLightHelper = new THREE.DirectionalLightHelper(ctx.directionalLight, 10);
                ctx.scene.add(dirLightHelper);
                // 添加方向光源阴影相机辅助对象
                const dirLightShadowHelper = new THREE.CameraHelper(ctx.directionalLight.shadow.camera);
                ctx.scene.add(dirLightShadowHelper);
            }

            // 添加点光源以增强立体感
            ctx.pointLight = new THREE.PointLight(0xffffff, 0.5, 1000);
            ctx.pointLight.position.set(-50, 100, -50);
            ctx.pointLight.castShadow = true;
            ctx.pointLight.shadow.mapSize.width = 1024;
            ctx.pointLight.shadow.mapSize.height = 1024;
            ctx.scene.add(ctx.pointLight);

            if (ctx.enableDevViewport) {
                // 添加点光源辅助对象
                const pointLightHelper = new THREE.PointLightHelper(ctx.pointLight, 5);
                ctx.scene.add(pointLightHelper);
            }
        }

        // 导入3d模型
        async function import3DModel(ctx) {
            // 设置纹理加载路径
            const manager = new THREE.LoadingManager();

            const loader = new FBXLoader(manager);
            const model = await new Promise((resolve) => {
                loader.load(
                    '../data/import-model/tree-0/base.fbx', // 替换为你的模型文件路径
                    function (object) {
                        console.log('模型加载成功');

                        // 手动加载纹理
                        const textureLoader = new THREE.TextureLoader();
                        const texture = textureLoader.load('../data/import-model/tree-0/shaded.png',
                            function (texture) {
                                console.log('纹理加载成功');
                            },
                            function (progress) {
                                console.log('纹理加载进度:', progress);
                            },
                            function (error) {
                                console.error('纹理加载失败:', error);
                            }
                        );

                        // 检查并修复材质
                        object.traverse((child) => {
                            if (child.isMesh) {
                                child.castShadow = true;
                                child.receiveShadow = true;
                                console.log('网格名称:', child.name);
                                console.log('材质信息:', child.material);

                                if (child.material) {
                                    // 如果是数组材质
                                    if (Array.isArray(child.material)) {
                                        child.material.forEach((mat, index) => {
                                            console.log(`材质${index}:`, mat);
                                            if (!mat.map) {
                                                mat.map = texture;
                                                mat.needsUpdate = true;
                                            }
                                        });
                                    } else {
                                        // 单个材质
                                        if (!child.material.map) {
                                            child.material.map = texture;
                                            child.material.needsUpdate = true;
                                            console.log('已应用纹理到材质');
                                        }
                                    }
                                }
                            }
                        });

                        resolve(object);
                    },
                    function (xhr) {
                        // 加载进度回调（可选）
                        console.log((xhr.loaded / xhr.total * 100) + '% loaded');
                    },
                    function (error) {
                        // 加载错误回调（可选）
                        console.error('An error happened', error);
                    }
                );
            })

            // 将模型添加到场景中
            ctx.scene.add(model);

            // 计算模型边界盒以调整相机位置
            const box = new THREE.Box3().setFromObject(model);
            const center = box.getCenter(new THREE.Vector3());
            const size = box.getSize(new THREE.Vector3());

            // 将模型底部对齐到原点
            model.position.x = -center.x;
            model.position.z = -center.z;
            model.position.y = -box.min.y;  // 将底部对齐到y=0平面

            // // 根据模型大小调整相机位置
            // const maxDim = Math.max(size.x, size.y, size.z);
            // const fov = ctx.camera.fov * (Math.PI / 180);
            // let cameraZ = Math.abs(maxDim / 2 / Math.tan(fov / 2));
            // cameraZ *= 2; // 稍微拉远一些
            // ctx.camera.position.set(cameraZ, cameraZ, cameraZ);

            console.log('模型加载完成，尺寸:', size, '相机位置:', ctx.camera.position);

            return model;
        }

        // 构建游戏3d模型
        async function buildGame3DModel(ctx) {
            // 添加地面
            const groundGeometry = new THREE.PlaneGeometry(1000, 1000);
            const groundMaterial = new THREE.MeshStandardMaterial({ color: 0x808080 });
            const ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.rotation.x = -Math.PI / 2;
            ground.position.y = 0;
            ground.receiveShadow = true;
            ctx.scene.add(ground);
        }

        async function main() {
            // *** 初始化图形引擎 ***
            await initGraphicsEngine(ctx);

            // *** 加载光源 ***
            await loadLightSource(ctx);

            // *** 导入3d模型 ***
            await import3DModel(ctx);

            // *** 构建游戏3d模型 ***
            await buildGame3DModel(ctx);

            // 调整相机初始朝向
            ctx.camera.position.set(0, 100, 500);
            ctx.camera.lookAt(0, 0, 0);

            animate();
        }

        main();
    </script>
</body>

</html>