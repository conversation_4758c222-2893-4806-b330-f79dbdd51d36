(function (global, factory) {
  if (typeof define === "function" && define.amd) {
    define('element/locale/ta', ['module', 'exports'], factory);
  } else if (typeof exports !== "undefined") {
    factory(module, exports);
  } else {
    var mod = {
      exports: {}
    };
    factory(mod, mod.exports);
    global.ELEMENT.lang = global.ELEMENT.lang || {}; 
    global.ELEMENT.lang.ta = mod.exports;
  }
})(this, function (module, exports) {
  'use strict';

  exports.__esModule = true;
  exports.default = {
    el: {
      colorpicker: {
        confirm: 'உறுதி செய்',
        clear: 'தெளிவாக்கு'
      },
      datepicker: {
        now: 'தற்போது',
        today: 'இன்று',
        cancel: 'ரத்து செய்',
        clear: 'சரி',
        confirm: 'உறுதி செய்',
        selectDate: 'தேதியை தேர்வு செய்',
        selectTime: 'நேரத்தை தேர்வு செய்',
        startDate: 'தொடங்கும் நாள்',
        startTime: 'தொடங்கும் நேரம்',
        endDate: 'முடியும் தேதி',
        endTime: 'முடியும் நேரம்',
        prevYear: 'Previous Year', // to be translated
        nextYear: 'Next Year', // to be translated
        prevMonth: 'Previous Month', // to be translated
        nextMonth: 'Next Month', // to be translated
        year: 'வருடம்',
        month1: 'ஜனவரி',
        month2: 'பிப்ரவரி',
        month3: 'மார்ச்',
        month4: 'ஏப்ரல்',
        month5: 'மே',
        month6: 'ஜூன்',
        month7: 'ஜூலை',
        month8: 'ஆகஸ்ட்',
        month9: 'செப்டம்பர்',
        month10: 'அக்டோபர்',
        month11: 'நவம்பர்',
        month12: 'டிசம்பர்',
        weeks: {
          sun: 'ஞாயிறு',
          mon: 'திங்கள்',
          tue: 'செவ்வாய்',
          wed: 'புதன்',
          thu: 'வியாழன்',
          fri: 'வெள்ளி',
          sat: 'சனி'
        },
        months: {
          jan: 'ஜனவரி',
          feb: 'பிப்ரவரி',
          mar: 'மார்ச்',
          apr: 'ஏப்ரல்',
          may: 'மே',
          jun: 'ஜூன்',
          jul: 'ஜூலை',
          aug: 'ஆகஸ்ட்',
          sep: 'செப்டம்பர்',
          oct: 'அக்டோபர்',
          nov: 'நவம்பர்',
          dec: 'டிசம்பர்'
        }
      },
      select: {
        loading: 'தயாராகிக்கொண்டிருக்கிறது',
        noMatch: 'பொருத்தமான தரவு கிடைக்கவில்லை',
        noData: 'தரவு இல்லை',
        placeholder: 'தேர்வு செய்'
      },
      cascader: {
        noMatch: 'பொருத்தமான தரவு கிடைக்கவில்லை',
        loading: 'தயாராகிக்கொண்டிருக்கிறது',
        placeholder: 'தேர்வு செய்',
        noData: 'தரவு இல்லை'
      },
      pagination: {
        goto: 'தேவையான் பகுதிக்கு செல்',
        pagesize: '/page',
        total: 'மொத்தம் {total}',
        pageClassifier: ''
      },
      messagebox: {
        title: 'செய்தி',
        confirm: 'உறுதி செய்',
        cancel: 'ரத்து செய்',
        error: 'பொருத்தாமில்லாத உள்ளீடு'
      },
      upload: {
        deleteTip: 'press delete to remove', // to be translated
        delete: 'நீக்கு',
        preview: 'முன்னோட்டம் பார்',
        continue: 'தொடரு'
      },
      table: {
        emptyText: 'தரவு இல்லை',
        confirmFilter: 'உறுதி செய்',
        resetFilter: 'புதுமாற்றம் செய்',
        clearFilter: 'அனைத்தும்',
        sumText: 'கூட்டு'
      },
      tree: {
        emptyText: 'தரவு இல்லை'
      },
      transfer: {
        noMatch: 'பொருத்தமான தரவு கிடைக்கவில்லை',
        noData: 'தரவு இல்லை',
        titles: ['பட்டியல் 1', 'பட்டியல் 2'],
        filterPlaceholder: 'சொல்லை உள்ளீடு செய்',
        noCheckedFormat: '{total} items', // to be translated
        hasCheckedFormat: '{checked}/{total} தேர்வு செய்யப்பட்டவைகள்'
      },
      image: {
        error: 'FAILED' // to be translated
      },
      pageHeader: {
        title: 'Back' // to be translated
      },
      popconfirm: {
        confirmButtonText: 'Yes', // to be translated
        cancelButtonText: 'No' // to be translated
      },
      empty: {
        description: 'தரவு இல்லை'
      }
    }
  };
  module.exports = exports['default'];
});