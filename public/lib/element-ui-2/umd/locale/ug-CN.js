(function (global, factory) {
  if (typeof define === "function" && define.amd) {
    define('element/locale/ug-CN', ['module', 'exports'], factory);
  } else if (typeof exports !== "undefined") {
    factory(module, exports);
  } else {
    var mod = {
      exports: {}
    };
    factory(mod, mod.exports);
    global.ELEMENT.lang = global.ELEMENT.lang || {}; 
    global.ELEMENT.lang.ugCN = mod.exports;
  }
})(this, function (module, exports) {
  'use strict';

  exports.__esModule = true;
  exports.default = {
    el: {
      colorpicker: {
        confirm: 'جەزملەش',
        clear: 'قۇرۇقداش'
      },
      datepicker: {
        now: 'ھازىرقى ۋاقىت',
        today: 'بۈگۈن',
        cancel: 'بىكار قىلىش',
        clear: 'قۇرۇقداش',
        confirm: 'جەزملەش',
        selectDate: 'چىسلا تاللاڭ',
        selectTime: 'ۋاقىت تاللاڭ',
        startDate: 'باشلانغان چىسلا',
        startTime: 'باشلانغان ۋاقىت',
        endDate: 'ئاخىرلاشقان چىسلا',
        endTime: 'ئاخىرلاشقان ۋاقىت',
        prevYear: 'ئالدىنقى يىل',
        nextYear: 'كىيىنكى يىل',
        prevMonth: 'ئالدىنقى ئاي',
        nextMonth: 'كىيىنكى ئاي',
        year: '- يىل',
        month1: '1-ئاي',
        month2: '2-ئاي',
        month3: '3-ئاي',
        month4: '4-ئاي',
        month5: '5-ئاي',
        month6: '6-ئاي',
        month7: '7-ئاي',
        month8: '8-ئاي',
        month9: '9-ئاي',
        month10: '10-ئاي',
        month11: '11-ئاي',
        month12: '12-ئاي',
        // week: '周次',
        weeks: {
          sun: 'يەكشەنبە',
          mon: 'دۈشەنبە',
          tue: 'سەيشەنبە',
          wed: 'چارشەنبە',
          thu: 'پەيشەنبە',
          fri: 'جۈمە',
          sat: 'شەنبە'
        },
        months: {
          jan: '1-ئاي',
          feb: '2-ئاي',
          mar: '3-ئاي',
          apr: '4-ئاي',
          may: '5-ئاي',
          jun: '6-ئاي',
          jul: '7-ئاي',
          aug: '8-ئاي',
          sep: '9-ئاي',
          oct: '10-ئاي',
          nov: '11-ئاي',
          dec: '12-ئاي'
        }
      },
      select: {
        loading: 'يۈكلىنىۋاتىدۇ',
        noMatch: 'ئۇچۇر تېپىلمىدى',
        noData: 'ئۇچۇر يوق',
        placeholder: 'تاللاڭ'
      },
      cascader: {
        noMatch: 'ئۇچۇر تېپىلمىدى',
        loading: 'يۈكلىنىۋاتىدۇ',
        placeholder: 'تاللاڭ',
        noData: 'ئۇچۇر يوق'
      },
      pagination: {
        goto: 'كىيىنكى بەت',
        pagesize: 'تال/بەت',
        total: 'جەمئىي {total} تال',
        pageClassifier: 'بەت'
      },
      messagebox: {
        title: 'ئەسكەرتىش',
        confirm: 'جەزملەش',
        cancel: 'بىكار قىلىش',
        error: 'كىرگۈزگەن ئۇچۇرىڭىزدا خاتالىق بار!'
      },
      upload: {
        deleteTip: 'delete كۇنپكىسىنى بېسىپ ئۆچۈرەلەيسىز',
        delete: 'ئۆچۈرۈش',
        preview: 'رەسىمنى كۆرۈش',
        continue: 'رەسىم يوللاش'
      },
      table: {
        emptyText: 'ئۇچۇر يوق',
        confirmFilter: 'سۈزگۈچ',
        resetFilter: 'قايتا تولدۇرۇش',
        clearFilter: 'ھەممە',
        sumText: 'جەمئىي'
      },
      tree: {
        emptyText: 'ئۇچۇر يوق'
      },
      transfer: {
        noMatch: 'ئۇچۇر تېپىلمىدى',
        noData: 'ئۇچۇر يوق',
        titles: ['جەدۋەل 1', 'جەدۋەل 2'],
        filterPlaceholder: 'ئىزدىمەكچى بولغان مەزمۇننى كىرگۈزۈڭ',
        noCheckedFormat: 'جەمئىي {total} تۈر',
        hasCheckedFormat: 'تاللانغىنى {checked}/{total} تۈر'
      },
      image: {
        error: 'مەغلۇب بولدى'
      },
      pageHeader: {
        title: 'قايتىش'
      },
      popconfirm: {
        confirmButtonText: 'ھەئە',
        cancelButtonText: 'ياق'
      },
      empty: {
        description: 'ئۇچۇر يوق'
      }
    }
  };
  module.exports = exports['default'];
});