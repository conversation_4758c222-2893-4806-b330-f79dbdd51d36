<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title></title>
    <meta name='viewport'
        content='width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no' />
    <link href="" rel="stylesheet">
    <style>
        body {
            overflow: hidden;
            margin: 0;
            padding: 0;
            background-color: #000;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: Arial, sans-serif;
        }

        #gameCanvas {
            border: 1px solid #fff;
            background-color: #333;
        }
    </style>
    <script src="./src/main-start.js"></script>
</head>

<body>
    <canvas id="gameCanvas" width="800" height="600"></canvas>
    <script type="importmap">
        {
            "imports": {
                "three": "./lib/threejs/build/three.module.js",
                "three/webgpu": "./lib/threejs/build/three.webgpu.js",
                "three/tsl": "./lib/threejs/build/three.tsl.js",
                "three/addons/CSS2DRenderer": "./lib/threejs/examples/jsm/renderers/CSS2DRenderer.js",
                "three/addons/FBXLoader": "./lib/threejs/examples/jsm/loaders/FBXLoader.js",
                "three/addons/OrbitControls": "./lib/threejs/examples/jsm/controls/OrbitControls.js",
                "three/addons/Sky": "./lib/threejs/examples/jsm/objects/Sky.js",
                "cannon-es": "./lib/cannon-es/cannon-es.js",
                "cannon-es-debugger": "./lib/cannon-es/cannon-es-debugger.js"
            }
        }
    </script>
    <script>
        /*
        */
    </script>
    <script type="module">
        import * as THREE from 'three';
        import { CSS2DRenderer } from "three/addons/CSS2DRenderer";
        import { CSS2DObject } from "three/addons/CSS2DRenderer";
        import { OrbitControls } from "three/addons/OrbitControls";
        import { FBXLoader } from "three/addons/FBXLoader";
        import { Sky } from "three/addons/Sky";

        import * as CANNON from 'cannon-es';
        import CannonDebugger from 'cannon-es-debugger';

        start({
            THREE, CSS2DRenderer, CSS2DObject, FBXLoader,
            OrbitControls,
            Sky,
            CANNON, CannonDebugger,
        }, {
            gameType: '3d',
        })
    </script>
</body>

</html>