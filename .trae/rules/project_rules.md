- 基于 threejs + html 开发的游戏
- index.html 或 index_**.html 是启动文件
- ctx是Context类的简写变量名，用于存放全局对象，例：scene 默认场景，camera 默认相机，renderer, enableDevViewport ...
- enableDevViewport为true时启动开发者视口，用户通过开发者相机看到游戏场景内布局，默认相机，光源等都会以指示器方式呈现， 为false就是游戏正式运行，用户通过默认相机玩游戏
- 导入包可以从ctx.imports获取，例：const { THREE, OrbitControls } = this.ctx.imports;
- index_dev.html
    - toolBar 工具栏
        - vue脚本在 src/view-model/ToolBarViewModel.js 中
        - 样式源码在 public/assets/css/dev-ui.less 中，.css文件是编译后内容，不要修改
    - propBar 属性栏
        - vue脚本在 src/view-model/PropBarViewModel.js 中
        - 样式源码在 public/assets/css/dev-ui.less 中，.css文件是编译后内容，不要修改
