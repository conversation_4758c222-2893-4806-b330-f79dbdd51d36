import {UserActionLogService} from "./UserActionLogService";

/**
 * 短时间缓存服务
 * （如果一段时间不用的缓存就会自动消除）
 */
export default class ShortTimeCacheService {

    constructor(ctx) {
        this.ctx = ctx;
        this._cache = {};
        this._timeout = 1000 * 60;

        this.thread();
    }

    /**
     *
     * @returns {*}
     */
    getUserActionLogService() {
        const self = this;
        const key = 'userActionLogService';

        return this.get(key, () => {
            const ctx = self.ctx;
            return new UserActionLogService(ctx, ctx.config);
        });
    }

    /**
     * 获取花村并更新使用时间
     * @param key
     * @param newFunc
     * @returns {*}
     */
    get(key, newFunc) {
        const cache = this._cache[key];
        if (cache == null) {
            const t = newFunc();
            this._cache[key] = {
                data: t,
                time: Date.now()
            };
            return t;
        }
        else {
            cache.time = Date.now();
            return cache.data;
        }
    }

    /**
     * 线程
     */
    thread() {
        const self = this;

        for (const prop in this._cache) {
            const obj = this._cache[prop];
            if (Date.now() - obj.time > self._timeout) {
                delete this._cache[prop];
            }
        }

        setTimeout(() => {
            self.thread();
        }, 1000 * 10);
    }
}