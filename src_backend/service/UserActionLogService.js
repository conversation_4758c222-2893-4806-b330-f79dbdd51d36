import TaskProcQueue from "../util/TaskProcQueue";
import {toDateTimeStr, toYearDateDirName} from "../util/time-util";

/**
 * 行为日志
 */
export class UserActionLogService {

    constructor(ctx) {
        this.ctx = ctx;
        this._taskProcQueue = new TaskProcQueue({
            // debug: true,
        });
        this._taskProcQueue.start();
    }

    /**
     * 推送记录
     * @param groupKey
     * @param msg
     */
    push(groupKey, msg) {
        const self = this;

        this._taskProcQueue.push({
            name: 'push_user_action_log',
            pars: {
                groupKey, msg,
            },
            proc(pars, cb) {
                try {
                    const { fs, path } = self.ctx.imports;

                    const groupDirPath = self._getSaveDirPath(pars.groupKey);
                    const filePath = `${groupDirPath}${toYearDateDirName(new Date())}.txt`;
                    const dirPath = path.dirname(filePath);

                    if (!fs.existsSync(dirPath)) {
                        fs.mkdirSync(dirPath, { recursive: true });
                    }

                    fs.appendFileSync(filePath, `${toDateTimeStr(new Date())} ${msg}\n`);
                } catch (exc) {
                    log(`error -> ${exc.message} -> ${exc.stack}`)
                }

                cb();
            }
        })
    }

    _getStartDirName() {
        return 'user-action-log';
    }

    _getSaveDirPath(groupKey) {
        return `${this.ctx.rootPath}/data/${this._getStartDirName()}/${groupKey}/`
    }
}