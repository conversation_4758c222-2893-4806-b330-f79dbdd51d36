import {toDateTimeStr} from "./util/time-util";
import SystemService from "./service/SystemService";
import { CSHotspringTpl3dService } from "./module/game-editor/service/CSHotspringTpl3dService";


export function start(imports, opts = {}) {
  if (opts == null) opts = {};
  const {
    express, 
    // session, cookieParser, 
    bodyParser, fs, path, 
    // lancedb, apacheArrow,
  } = imports;

  global.log = function log(...msg) {
    let t = '';
    t = t.concat(toDateTimeStr(new Date()));
    t = t.concat(' ');
    msg.forEach((n) => {
      t = t.concat(n);
    });
    console.log(t);
  }

  const ctx = {
    imports: {
      ...imports,
      // lancedb, apacheArrow,
    }
  };
  const systemService = new SystemService(ctx);
  systemService.init({
    rootPath: opts.rootPath,
  });

  log('*** csaui-app ***');
  log(`Version: ${ctx.config.version}`);
  log(`Env: ${ctx.config.env}`);
  // 执行程序所在目录
  log(`RootPath: ${ctx.rootPath}`);
  // 用户触发执行程序所在目录
  log(`UserExecDirPath: ${path.resolve()}`)

  if (opts.startServer !== false) {
    // *** 启动服务 ***
    const app = express();

    //解决跨域
    app.all('*', function (req, res, next) {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Headers', 'Content-Type, Content-Length, Authorization, Accept, X-Requested-With');
      res.header('Access-Control-Allow-Methods', 'PUT, POST, GET, DELETE, OPTIONS');
      if (req.method === 'OPTIONS') {
        res.sendStatus(200);
      } else {
        next();
      }
    });

    // *** 全局请求日志中间件 ***
    if (ctx.config.env === 'dev') {
    }
    else {
    }

    // *** 静态托管（允许直接访问静态资源） ***
    app.use('/', express.static(ctx.rootPath + '/public', {
      setHeaders: function (res, path, stat) {
        res.set('Access-Control-Allow-Origin', '*');
      }
    }));
    // 用于获取post表单参数
    app.use(bodyParser.urlencoded({extended: false}));
    // 用于获取body是json的数据
    app.use(bodyParser.json({limit: '10mb', extended: true}));


    ctx.csHotspringTpl3dService = new CSHotspringTpl3dService(ctx);
    ctx.csHotspringTpl3dService.start({
      app
    })
    

    // *** 配置路由 ***
    // 接收get请求
    app.get('/api/test/0', function (req, res) {
      // 获取url参数，例：/?lang=zh-CN
      var lang = req.query.lang;
      // 获取url参数，例：/:lang/:page
      var lang = req.params.lang;
      var page = req.params.page;
      res.send("1");
      // res.send(fs.readFileSync('./view/index.html', 'utf-8'));
    });

    const port = ctx.config.port;
    app.listen(port, () => {
      log(`server started at ${port}`)
    });
  }
  else {
  }

  return ctx;
}