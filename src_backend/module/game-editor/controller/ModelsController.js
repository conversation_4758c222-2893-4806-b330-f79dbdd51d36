import TopController from "../../../controller/TopController";

export default class ModelsController extends TopController {
    constructor(ctx, app, opts = {}) {
        super(ctx);
        const self = this;

        /**
         * 加载模型列表
         */
        app.post('/api/game-editor/models/loadList', async function (req, res) {
            self.logReq(req, '/api/game-editor/models/loadList');
            try {
                const list = await opts.parent.modelsMapper.loadList({
                    gameType: req.body.gameType,
                });

                res.send({
                    success: true,
                    data: list,
                });
            } catch (exc) {
                self.replyException(res, exc);
            }
        });
    }

}