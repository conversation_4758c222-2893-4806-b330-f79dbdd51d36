import TopController from "../../../controller/TopController";

export default class ProjectController extends TopController {
    constructor(ctx, app, opts = {}) {
        super(ctx);
        const self = this;

        /**
         * 保存项目
         * req.body.id 项目id
         * req.body.name 项目名称
         * req.body.data 项目数据
         */
        app.post('/api/game-editor/project/save', async function (req, res) {
            self.logReq(req, '/api/game-editor/project/save');
            try {
                const id = req.body.id;
                const name = req.body.name;
                const data = req.body.data;

                await opts.parent.projectMapper.save(id, name, data);

                res.send({
                    success: true,
                });
            } catch (exc) {
                self.replyException(res, exc);
            }
        });

        /**
         * 删除项目
         * req.body.id 项目id
         */
        app.post('/api/game-editor/project/delete', async function (req, res) {
            self.logReq(req, '/api/game-editor/project/delete');
            try {
                const id = req.body.id;

                await opts.parent.projectMapper.delete(id);

                res.send({
                    success: true,
                });
            } catch (exc) {
                self.replyException(res, exc);
            }
        });

        /**
         * 加载项目
         * req.body.id 项目id
         */
        app.post('/api/game-editor/project/load', async function (req, res) {
            self.logReq(req, '/api/game-editor/project/load');
            try {
                const id = req.body.id;

                const project = await opts.parent.projectMapper.load(id);

                res.send({
                    success: true,
                    data: project,
                });
            } catch (exc) {
                self.replyException(res, exc);
            }
        });

        /**
         * 加载最近打开过的项目
         * req.body.limit 限定条数
         */
        app.post('/api/game-editor/project/loadRecentList', async function (req, res) {
            self.logReq(req, '/api/game-editor/project/loadRecentList');
            try {
                const limit = req.body.limit;

                const list = await opts.parent.projectMapper.loadRecentList(limit);

                res.send({
                    success: true,
                    data: list,
                });
            } catch (exc) {
                self.replyException(res, exc);
            }
        });

        /**
         * 加载项目列表
         */
        app.post('/api/game-editor/project/loadList', async function (req, res) {
            self.logReq(req, '/api/game-editor/project/loadList');
            try {
                const list = await opts.parent.projectMapper.loadList();

                res.send({
                    success: true,
                    data: list,
                });
            } catch (exc) {
                self.replyException(res, exc);
            }
        });
    }

}