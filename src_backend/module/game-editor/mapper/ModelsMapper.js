/**
 * 模型库
 */
export class ModelsMapper {

    constructor(ctx) {
        this.ctx = ctx;
    }

    /**
     * 加载模型列表
     * @returns 模型列表
     */
    async loadList(pars) {
        const { fs, path } = this.ctx.imports;
        const dirPath = this.getDirPath();
        const filePath = `${dirPath}/dev/game-${pars.gameType}-models.jsonl`;
        if (!fs.existsSync(filePath)) {
            return null;
        }
        const content = fs.readFileSync(filePath, { encoding: 'utf-8' });
        const lines = content.split('\n');
        const list = [];
        for (const line of lines) {
            if (line && line.length > 0) {
                list.push(JSON.parse(line));
            }
        }

        return list;
    }


    getDirPath() {
        return `${this.ctx.rootPath}/data`
    }
}