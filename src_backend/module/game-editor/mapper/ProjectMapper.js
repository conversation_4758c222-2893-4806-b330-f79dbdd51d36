export class ProjectMapper {

    constructor(ctx) {
        this.ctx = ctx;
    }

    /**
     * 保存项目
     * 把id和name作为json对象保存在文件中第一行，data放到第二行
     * @param {*} id 项目id
     * @param {*} name 项目名称
     * @param {*} data 项目数据
     */
    async save(id, name, data) {
        const { fs, path } = this.ctx.imports;
        const dirPath = this.getDirPath();
        const filePath = `${dirPath}/${id}.jsonl`;

        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
        }
        
        const content = `${JSON.stringify({ id, name })}\n${JSON.stringify(data)}`;

        fs.writeFileSync(filePath, content, { encoding: 'utf-8' });
    }

    /**
     * 删除项目
     * @param {*} id 项目id
     */
    async delete(id) {
        const { fs, path } = this.ctx.imports;
        const dirPath = this.getDirPath();
        const filePath = `${dirPath}/${id}.jsonl`;

        if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
        }
    }

    /**
     * 加载项目
     * @param {*} id 项目id
     * @returns 项目数据
     */
    async load(id) {
        const { fs, path } = this.ctx.imports;
        const dirPath = this.getDirPath();
        const filePath = `${dirPath}/${id}.jsonl`;

        if (!fs.existsSync(filePath)) {
            return null;
        }

        const content = fs.readFileSync(filePath, { encoding: 'utf-8' });
        const lines = content.split('\n');
        const meta = JSON.parse(lines[0]);  
        const data = JSON.parse(lines[1]);
        return {
            id: meta.id,
            name: meta.name,
            data,
        }
    }

    /**
     * 加载最近打开过的项目列表
     * @param limit 限定条数
     * @returns {Promise<Array>}
     */
    async loadRecentList(limit) {
        const self = this;
        const { fs, path } = this.ctx.imports;
        const dirPath = this.getDirPath();

        if (!fs.existsSync(dirPath)) {
            return [];
        }

        let files = fs.readdirSync(dirPath)
            .filter(file => file.endsWith('.jsonl'));

        let files1 = []
        for (const file of files) {
            const filePath = path.join(dirPath, file);
            const stat = fs.statSync(filePath);

            const firstLine = await self._readFirstLine(filePath);
            const t = JSON.parse(firstLine);
            files1.push({
                ...t,
                modifiedTime: stat.mtime,
            })
        }
        files1.sort((a, b) => b.modifiedTime - a.modifiedTime);

        return files1.slice(0, limit);
    }

    /**
     * 加载项目列表
     * @returns {Promise<Array>}
     */
    async loadList() {
        const self = this;
        const { fs, path } = this.ctx.imports;
        const dirPath = this.getDirPath();

        if (!fs.existsSync(dirPath)) {
            return [];
        }

        const files = fs.readdirSync(dirPath)
            .filter(file => file.endsWith('.jsonl'));

        let files1 = []
        for (const file of files) {
            const filePath = path.join(dirPath, file);
            const stat = fs.statSync(filePath);

            const firstLine = await self._readFirstLine(filePath);
            const t = JSON.parse(firstLine);
            files1.push({
                ...t,
                modifiedTime: stat.mtime,
            })
        }
        files1.sort((a, b) => b.modifiedTime - a.modifiedTime);

        return files1;
    }

    async _readFirstLine(filePath) {
        const { fs, path, readline } = this.ctx.imports;
        const fileStream = fs.createReadStream(filePath);

        const rl = readline.createInterface({
            input: fileStream,
            crlfDelay: Infinity
        });

        for await (const line of rl) {
            rl.close(); // 关闭读取流
            return line; // 返回第一行
        }

        return null; // 如果文件为空
    }

    getDirPath() {
        return `${this.ctx.rootPath}/data/project`
    }
}