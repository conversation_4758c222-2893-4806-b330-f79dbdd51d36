import TopObject from "../../../model/TopObject";
import ModelsController from "../controller/ModelsController";
import ProjectController from "../controller/ProjectController";
import { ModelsMapper } from "../mapper/ModelsMapper";
import { ProjectMapper } from "../mapper/ProjectMapper";

export class CSHotspringTpl3dService extends TopObject {

    constructor(ctx) {
        super(ctx);
    }

    start(pars) {
        const { app } = pars;

        new ProjectController(this.ctx, app, {
            parent: this,
        });
        new ModelsController(this.ctx, app, {
            parent: this,
        });

        this.projectMapper = new ProjectMapper(this.ctx);
        this.modelsMapper = new ModelsMapper(this.ctx);
    }
}