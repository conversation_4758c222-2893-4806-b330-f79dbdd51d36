import { CGObjectModel } from "../model/CGObjectModel";
import { tjs_getCameraToXZPos } from "../util/tjs-camera-util";

export class ToolBarViewModel {
    constructor(ctx) {
        this.ctx = ctx;

        this._vue;
    }

    async build() {
        const { ctx } = this;
        const vmSelf = this;
        this._vue = new Vue({
            el: '#toolBar',
            data: function () {
                return {
                    models: [],
                    activeTabName: 'models',
                };
            },
            methods: {
                switchTab(tabName) {
                    this.activeTabName = tabName;
                },
                async loadData() {
                    const models = await vmSelf.ctx.modelsStorageMapper.loadList();
                    this.models = models;

                    // 测试
                    // for (let i = 0; i < 100; i++) {
                    //     this.models.push(models[0])
                    // }
                },
                getModelIcon(item) {
                    if (item.icon == null || item.icon.length === 0) {
                        return './assets/img/model.svg';
                    }
                    
                    if (item.type === 'import-model') {
                        return `./data/import-model/${item.id}${item.icon}`;
                    }
                    else {
                        return `./data/custom-model/${item.id}${item.icon}`;
                    }
                },
                getModelType(item) {
                    if (item.type === 'import-model') {
                        return '导入';
                    }
                    else {
                        return '内置';
                    }
                },
                getTags(item) {
                    return item.tags.toString().replace(/[,]/g, '，');
                },
                async handleItemClick(item) {
                    // 获取镜头朝向的交叉点
                    const pos = tjs_getCameraToXZPos(ctx, ctx.camera);
                    // 获取网格中心点
                    const pos1 = self.ctx.utils.getDesignGroundGridRealPosByRealPos(pos);
                    console.log('添加模型', item, pos1);

                    if (item.type === 'import-model') {
                        await ctx.cgWorld.addObjectAndBuild(new CGObjectModel(ctx, {
                            tags: item.tags,
                            // posUnit: [0, 0, 0],
                            pos: pos1,
                            modelLoader: {
                                url: `./data/import-model/${item.id}`
                            },
                            collidableObject: {},
                            ...item.data,
                        }));
                    }
                    else if (item.type.endsWith('-object-build')) {
                        let typeName = item.type.replace('-object-build', '');
                        await ctx.cgWorld.addObjectAndBuild(new ctx.objectTypes[typeName](ctx, {
                            tags: item.tags,
                            ...item.data,
                            pos: pos1,
                        }));
                    }

                    ctx.cgWorld.getAbility('get_user_change_any_event').touchEvent('user-add-object', {});
                },
            },
            created: function () {
            },
            mounted: async function () {
                var self = this;
                await self.loadData();
                this.$nextTick(function () {
                });
            }
        });
    }
}