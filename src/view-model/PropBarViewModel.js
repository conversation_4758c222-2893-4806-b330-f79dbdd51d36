export class PropBarViewModel {
    constructor(ctx) {
        this.ctx = ctx;

        this._vue;
    }

    async build() {
        const { ctx } = this;
        const vmSelf = this;
        this._vue = new Vue({
            el: '#propBar',
            data: function () {
                const selectedProps = {};
                for (const type in ctx.objectTypes) {
                    const objectType = ctx.objectTypes[type];
                    const props = objectType.getProps();
                    for (const prop of props) {
                        selectedProps[prop.name] = null;
                    }
                }

                const playerProps = {};
                {
                    const props = vmSelf.getPlayerPropDesignList();
                    for (const prop of props) {
                        playerProps[prop.name] = null;
                    }
                }

                return {
                    // 激活Tab：selected | designer | player | world
                    activeTabName: 'selected',
                    focusPropName: null,

                    // selected
                    selectedObject: null,
                    selectedProps: selectedProps,
                    selectedPropDesignList: [],

                    // player
                    playerProps: playerProps,
                    playerPropDesignList: [],
                };
            },
            methods: {
                switchTab(tabName) {
                    this.activeTabName = tabName;

                    if (tabName === 'player') {
                        this.updatePlayerTab();
                    }
                },

                onUserSelectObject(e) {
                    this.selectedObject = e.target;
                    this.selectedProps.id = this.selectedObject.getId();

                    if (ctx.objectTypes[this.selectedObject.constructor.name].getProps) {
                        const props = ctx.objectTypes[this.selectedObject.constructor.name].getProps();
                        this.selectedPropDesignList = props;

                        for (const prop of props) {
                            let t = this.selectedObject.getPropValue(prop.name);
                            if (t === undefined) {
                                t = prop.default;
                            }
                            this.selectedProps[prop.name] = t;
                        }
                    }
                },
                onUserCancelSelectObject(e) {
                    this.selectedObject = null;
                    for (const prop in this.selectedProps) {
                        this.selectedProps[prop.name] = null;
                    }
                    this.selectedPropDesignList = [];
                },
                async saveProject() {
                    const loading = this.$loading({ lock: true, text: '处理中' });
                    await ctx.projectStorageService.save();
                    loading.close();
                },
                isFocusProp(name) {
                    return this.focusPropName === name;
                },
                handleFocusProp(name) {
                    if (this.focusPropName === name) {
                        this.focusPropName = null;
                    }
                    else {
                        this.focusPropName = name;
                    }
                },

                updateSelectedTab() {
                    if (this.selectedObject) {
                        if (ctx.objectTypes[this.selectedObject.constructor.name].getProps) {
                            const props = ctx.objectTypes[this.selectedObject.constructor.name].getProps();

                            for (const prop of props) {
                                let t = this.selectedObject.getPropValue(prop.name);
                                if (t === undefined) {
                                    t = prop.default;
                                }
                                this.selectedProps[prop.name] = t;
                            }
                        }
                    }
                },
                handleSelectedPropChange(name, e) {
                    this.selectedObject.setPropValue(name, e);
                    const gucaeAbility = ctx.cgWorld.getAbility('get_user_change_any_event')
                    gucaeAbility.touchEvent('user-change-object', {});
                },
                handleSelectedDelete(e) {
                    e.preventDefault(); // 阻止发生默认行为
                    e.stopPropagation(); // 阻止事件冒泡到父元素

                    var self = this;
                    if (this.selectedProps.id == null || this.selectedProps.id.length === 0) {
                        // alert('请先选中对象');
                        self.$alert('请先选中对象', { type: 'error' });
                        return;
                    }

                    self.$confirm('确定要删除选中的对象吗？', '确认提示', { type: 'warning' }).then(() => {
                        // 点击确认
                        ctx.cgWorld.removeObjectById(this.selectedProps.id);
                        const gucaeAbility = ctx.cgWorld.getAbility('get_user_change_any_event')
                        gucaeAbility.touchEvent('user-delete-object', {});
                    }).catch(() => {
                        // 点击取消
                    });
                },
                handleSelectedMouseWheel(name, e) {
                    if (this.focusPropName === name) {
                        e.preventDefault(); // 阻止发生默认行为
                        e.stopPropagation(); // 阻止事件冒泡到父元素

                        const focusPropDesign = this.selectedPropDesignList.find(n => n.name === this.focusPropName);
                        if (focusPropDesign && focusPropDesign.type === 'Number') {
                            let eachStepAmount = 1;
                            const findPropDesign = this.selectedPropDesignList.find(item => item.name == name);
                            if (findPropDesign && findPropDesign.step != null) {
                                eachStepAmount = findPropDesign.step;
                            }

                            if (e.deltaY < 0) {
                                let t = this.selectedProps[name] ?? 0;
                                this.$set(this.selectedProps, name, parseFloat((t + eachStepAmount).toFixed(2)));

                            } else {
                                let t = this.selectedProps[name] ?? 0;
                                this.$set(this.selectedProps, name, parseFloat((t - eachStepAmount).toFixed(2)));
                            }
                            this.selectedObject.setPropValue(name, this.selectedProps[name]);
                            const gucaeAbility = ctx.cgWorld.getAbility('get_user_change_any_event')
                            gucaeAbility.touchEvent('user-change-object', {});
                        }
                    }
                },
                handleSelectedKeydown(e) {
                    e.stopPropagation(); // 阻止事件冒泡到父元素
                },

                updatePlayerTab() {
                    this.playerProps = {
                        showCCI: ctx.data.player.showCCI,
                    };
                    this.playerPropDesignList = vmSelf.getPlayerPropDesignList();
                },
                handlePlayerPropChange(name, e) {
                    if (name === 'showCCI') {
                        vmSelf.ctx.data.player.showCenterIndicator = e;
                        const gucaeAbility = ctx.cgWorld.getAbility('get_user_change_any_event')
                        gucaeAbility.touchEvent('user-change-object', {});
                    }
                },
                handlePlayerMouseWheel(name, e) {
                    if (this.focusPropName === name) {
                        e.preventDefault(); // 阻止发生默认行为
                        e.stopPropagation(); // 阻止事件冒泡到父元素

                        const focusPropDesign = this.playerPropDesignList.find(n => n.name === this.focusPropName);
                        if (focusPropDesign && focusPropDesign.type === 'Number') {
                            let eachStepAmount = 1;

                            if (e.deltaY < 0) {
                                let t = this.playerProps[name] ?? 0;
                                this.$set(this.playerProps, name, parseFloat((t + eachStepAmount).toFixed(2)));

                            } else {
                                let t = this.playerProps[name] ?? 0;
                                this.$set(this.playerProps, name, parseFloat((t - eachStepAmount).toFixed(2)));
                            }
                            if (name === 'xxx') {
                            }

                            const gucaeAbility = ctx.cgWorld.getAbility('get_user_change_any_event')
                            gucaeAbility.touchEvent('user-change-object', {});
                        }
                    }
                }
            },
            created: function () {
            },
            mounted: function () {
                var self = this;

                // 监听用户选中对象事件
                const gucaeAbility = ctx.cgWorld.getAbility('get_user_change_any_event');
                if (gucaeAbility) {
                    gucaeAbility.addEventListener('pb_vm_0', ['user-select-object'], (e) => {
                        self.onUserSelectObject(e);
                    });
                    gucaeAbility.addEventListener('pb_vm_1', ['user-cancel-select-object'], (e) => {
                        self.onUserCancelSelectObject(e);
                    });
                }

                const selectedObject = ctx.cgWorld.getSelectedObject();
                if (selectedObject) {
                    self.onUserSelectObject({
                        target: selectedObject,
                    });
                }

                this.$nextTick(function () {
                });
            }
        });
    }

    showMessage(text) {
        if (this._vue) {
            this._vue.$message({ message: text, type: 'success' });
        }
    }

    updateSelectedTab() {
        if (this._vue) {
            this._vue.updateSelectedTab();
        }
    }

    updatePlayerTab() {
        if (this._vue) {
            this._vue.updatePlayerTab();
        }
    }

    getPlayerPropDesignList() {
        return [
            { label: '中心坐标指示器', title: '中心坐标指示器（CCI）', name: 'showCCI', type: 'Boolean', default: false },
        ]
    }
}