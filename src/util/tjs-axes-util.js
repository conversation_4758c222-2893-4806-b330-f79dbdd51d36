/**
 * 创建坐标指示器
 * @param {*} ctx 
 * @param {*} opts 
 */
export function tjs_createAxesIndicator(ctx, opts = {}) {
    const { THREE, CSS2DObject } = ctx.imports;

    let caiAxesSize = opts.caiAxesSize;
    let caiArrowRadius = opts.caiArrowRadius;

    // 中心点坐标指示器尺寸
    if (caiAxesSize == null) caiAxesSize = 200;
    if (caiArrowRadius == null) caiArrowRadius = 1;

    let arrowLabelDistance = opts.arrowLabelDistance || 5;

    const axesHelper = new THREE.AxesHelper(caiAxesSize);
    axesHelper.name = 'axesHelper';

    axesHelper.renderOrder = 1;
    axesHelper.position.set(0, 0, 0);

    {
        // x轴箭头（红色）
        const geometry = new THREE.ConeGeometry(caiArrowRadius, caiAxesSize / 5, 32);
        const material = new THREE.MeshBasicMaterial({ color: 'red' });
        const cone = new THREE.Mesh(geometry, material);
        cone.position.set(caiAxesSize, 0, 0);
        cone.rotateZ(-Math.PI / 180 * 90);
        axesHelper.add(cone);

        // x轴标签
        const labelDiv = document.createElement('div');
        labelDiv.textContent = '+X';
        labelDiv.style = 'color:silver;';
        const css2dLabel = new CSS2DObject(labelDiv);
        css2dLabel.position.set(caiAxesSize + arrowLabelDistance, 0, 0);
        axesHelper.add(css2dLabel);
    }
    {
        // y轴箭头（绿色）
        const geometry = new THREE.ConeGeometry(caiArrowRadius, caiAxesSize / 5, 32);
        const material = new THREE.MeshBasicMaterial({ color: 'green' });
        const cone = new THREE.Mesh(geometry, material);
        cone.position.set(0, caiAxesSize, 0);
        axesHelper.add(cone);

        // y轴标签
        const labelDiv = document.createElement('div');
        labelDiv.textContent = '+Y';
        labelDiv.style = 'color:silver;';
        const css2dLabel = new CSS2DObject(labelDiv);
        css2dLabel.position.set(0, caiAxesSize + arrowLabelDistance, 0);
        axesHelper.add(css2dLabel);
    }
    {
        // z轴箭头（蓝色）
        const geometry = new THREE.ConeGeometry(caiArrowRadius, caiAxesSize / 5, 32);
        const material = new THREE.MeshBasicMaterial({ color: 'blue' });
        const cone = new THREE.Mesh(geometry, material);
        cone.position.set(0, 0, caiAxesSize);
        cone.rotateX(Math.PI / 180 * 90);
        axesHelper.add(cone);

        // z轴标签
        const labelDiv = document.createElement('div');
        labelDiv.textContent = '+Z';
        labelDiv.style = 'color:silver;';
        const css2dLabel = new CSS2DObject(labelDiv);
        css2dLabel.position.set(0, 0, caiAxesSize + arrowLabelDistance);
        axesHelper.add(css2dLabel);
    }

    return axesHelper;
}

export function tjs_destroyAxesIndicator(axesHelper) {
    axesHelper.traverse(obj => {
        if (obj.isCSS2DObject && obj.element) {
            obj.element.remove(); // 从 DOM 中移除标签
        }
        if (obj.geometry) obj.geometry.dispose();
        if (obj.material) obj.material.dispose();
    });
    if (axesHelper.parent) {
        axesHelper.parent.remove(axesHelper);
    }
}
