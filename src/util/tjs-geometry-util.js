/**
 * 创建方形面
 * @param {*} ctx 
 * @param {*} width 宽度
 * @param {*} height 高度
 */
export function tjs_createPlane(ctx, width, height, opts = {}) {
    const { THREE } = ctx.imports;
    let { color, side, opacity, receiveShadow, castShadow } = opts;
    let texture = null;
    let video = null;

    // 显示的面
    if (side == null) side = THREE.DoubleSide;
    else if (side === 'front') side = THREE.FrontSide;
    else if (side === 'back') side = THREE.BackSide;

    // 图片纹理
    if (opts.imageSource && opts.imageSource.length > 0) {
        texture = new THREE.TextureLoader().load(opts.imageSource);
    }

    // 视频纹理
    if (opts.videoSource && opts.videoSource.length > 0) {
        video = document.createElement('video'); // 创建HTML视频元素
        video.src = opts.videoSource;                 // 设置视频源
        video.crossOrigin = "anonymous";
        video.preload = 'auto';
        video.loop = true;                           // 设置视频循环播放
        video.muted = true;                          // 设置视频静音
        // video.playsInline = true;                    // 设置视频内联播放，防止全屏
        // video.autoplay = true;                       // 设置视频自动播放
        // video.play();                                // 开始播放视频

        // source = document.createElement('source');
        // source.src = opts.videoSource;
        // source.type = 'video/mp4';
        // video.appendChild(source);

        // 视频纹理
        texture = new THREE.VideoTexture(video);
    }

    // 几何体
    const geometry = new THREE.PlaneGeometry(width, height);
    // 材质
    const material = new THREE.MeshStandardMaterial({
        map: texture,
        color: color || 'silver',
        // THREE.FrontSide | THREE.BackSide | THREE.DoubleSide
        side: side, // 两面都显示
        transparent: (opacity >= 0) || false,
        opacity: opacity || 1,
        // receiveShadow: receiveShadow || true,
    });
    // 网格对象
    const mesh = new THREE.Mesh(geometry, material);

    // 对齐点
    if (opts.alignmentPointType === 'bottom') {
        mesh.geometry.translate(0, (height / 2), 0);
    }

    if (receiveShadow != null) {
        mesh.receiveShadow = receiveShadow;
    }

    if (castShadow != null) {
        mesh.castShadow = castShadow;
    }

    if (opts.videoSource && opts.videoSource.length > 0) {
        mesh.userData.video = video;
    }

    return mesh;
}

/**
 * 创建圆形面
 * @param {*} ctx 
 * @param {*} radius 半径
 * @param {*} segments 分段数（越大越圆滑）
 * @param {*} opts 
 * @returns 
 */
export function tjs_createCirclePlane(ctx, radius, segments, opts = {}) {
    const { THREE } = ctx.imports;
    const { color, side, opacity, receiveShadow, castShadow } = opts;

    // 几何体
    // 参数：半径 = 1，分段数 = 32（越大越圆滑）
    const geometry = new THREE.CircleGeometry(radius || 1, segments || 32);
    // 材质
    const material = new THREE.MeshStandardMaterial({
        color: color || 0x00ff00,
        side: side || THREE.DoubleSide, // 两面都显示
        transparent: (opacity >= 0) || false,
        opacity: opacity || 1,
        // receiveShadow: receiveShadow || true,
    });
    // 网格对象
    const mesh = new THREE.Mesh(geometry, material);

    if (receiveShadow != null) {
        mesh.receiveShadow = receiveShadow;
    }

    if (castShadow != null) {
        mesh.castShadow = castShadow;
    }

    return mesh;
}

/**
 * 创建立方体
 * @param {*} ctx 
 * @param {*} width 宽度
 * @param {*} height 高度
 * @param {*} depth 深度
 * @param {*} opts 选项
 * @returns 
 */
export function tjs_createCube(ctx, width, height, depth, opts = {}) {
    const { THREE } = ctx.imports;
    const { color, opacity, receiveShadow, castShadow, widthSegments, heightSegments, depthSegments } = opts;

    // 几何体
    const geometry = new THREE.BoxGeometry(width || 1, height || 1, depth || 1, widthSegments, heightSegments, depthSegments);
    // 材质
    const material = new THREE.MeshStandardMaterial({
        color: color || 0xffffff,
        transparent: (opacity >= 0) || false,
        opacity: opacity || 1,
        // receiveShadow: receiveShadow || true,
        // castShadow: castShadow || true,
    });
    // 网格对象
    const mesh = new THREE.Mesh(geometry, material);

    if (receiveShadow != null) {
        mesh.receiveShadow = receiveShadow;
    }

    if (castShadow != null) {
        mesh.castShadow = castShadow;
    }

    return mesh;
}

/**
 * 创建圆桶形状
 * @param {*} ctx 
 * @param {*} topRadius 上底面的半径
 * @param {*} bottomRadius 下底面的半径
 * @param {*} height 高度
 * @param {*} opts 
 */
export function tjs_createCylinder(ctx, topRadius, bottomRadius, height, opts = {}) {
    const { THREE } = ctx.imports;
    const { color, opacity, receiveShadow, castShadow, radialSegments } = opts;

    // 几何体
    const geometry = new THREE.CylinderGeometry(topRadius, bottomRadius, height, radialSegments || 16);
    // 材质
    const material = new THREE.MeshStandardMaterial({
        color: color || 0xffffff,
        transparent: (opacity >= 0) || false,
        opacity: opacity || 1,
        // wireframe: true,
        // receiveShadow: receiveShadow || true,
        // castShadow: castShadow || true,
    });
    // 网格对象
    const mesh = new THREE.Mesh(geometry, material);

    if (receiveShadow != null) {
        mesh.receiveShadow = receiveShadow;
    }

    if (castShadow != null) {
        mesh.castShadow = castShadow;
    }

    return mesh;
}

/**
 * 创建球体
 * @param {*} ctx 
 * @param {*} radius 球体半径
 * @param {*} opts 选项
 * @returns 
 */
export function tjs_createSphere(ctx, radius, opts = {}) {
    const { THREE } = ctx.imports;
    const { color, opacity, receiveShadow, castShadow, widthSegments, heightSegments } = opts;

    // 几何体
    // 参数：半径, 水平分段数, 垂直分段数
    const geometry = new THREE.SphereGeometry(radius || 1, widthSegments || 32, heightSegments || 32);
    // 材质
    const material = new THREE.MeshStandardMaterial({
        color: color || 0xffffff,
        transparent: (opacity >= 0) || false,
        opacity: opacity || 1,
        // receiveShadow: receiveShadow || true,
        // castShadow: castShadow || true,
    });
    // 网格对象
    const mesh = new THREE.Mesh(geometry, material);

    if (receiveShadow != null) {
        mesh.receiveShadow = receiveShadow;
    }

    if (castShadow != null) {
        mesh.castShadow = castShadow;
    }

    return mesh;
}

/**
 * 根据两个坐标点创建圆桶形状
 * @param {*} ctx 
 * @param {*} topRadius 上底面的半径
 * @param {*} topPos 上底面的位置 Vector3
 * @param {*} bottomRadius 下底面的半径
 * @param {*} bottomPos 下底面的位置 Vector3
 * @param {*} opts 选项
 * @returns 
 */
export function tjs_createCylinderByTwoPoint(ctx, topRadius, bottomRadius, topPos, bottomPos, opts = {}) {
    const { THREE } = ctx.imports;
    const { color, opacity, receiveShadow, castShadow, radialSegments } = opts;

    const t_topPos = new THREE.Vector3();
    const t_bottomPos = new THREE.Vector3();

    t_topPos.x = topPos[0];
    t_topPos.y = topPos[1];
    t_topPos.z = topPos[2];

    t_bottomPos.x = bottomPos[0];
    t_bottomPos.y = bottomPos[1];
    t_bottomPos.z = bottomPos[2];



    // 计算两点之间的方向向量
    const direction = new THREE.Vector3().subVectors(t_topPos, t_bottomPos);
    // 计算高度（两点之间的距离）
    const height = direction.length();
    // 创建一个标准的圆柱体
    const cylinder = tjs_createCylinder(ctx, topRadius, bottomRadius, height, {
        color: color || 0xffffff,
        opacity: opacity,
        receiveShadow: receiveShadow,
        castShadow: castShadow,
        radialSegments: radialSegments
    });

    // 计算圆柱体的中心点位置（两点的中点）
    const center = new THREE.Vector3().addVectors(t_topPos, t_bottomPos).multiplyScalar(0.5);
    cylinder.position.copy(center);

    // 默认圆柱体的方向是沿着Y轴的，需要将其旋转到两点连线的方向
    // 创建一个表示Y轴正方向的向量
    const yAxis = new THREE.Vector3(0, 1, 0);
    // 归一化方向向量
    direction.normalize();
    // 计算旋转轴（叉乘得到垂直于两个向量的第三个向量）
    const rotationAxis = new THREE.Vector3().crossVectors(yAxis, direction);

    // 如果两点在Y轴上，叉乘结果接近零向量，需要特殊处理
    if (rotationAxis.length() < 0.001) {
        // 如果方向向量和Y轴同向，不需要旋转
        if (direction.y > 0) {
            // 不做任何旋转
        } else {
            // 如果方向向量和Y轴反向，旋转180度
            cylinder.rotateX(Math.PI);
        }
    } else {
        // 归一化旋转轴
        rotationAxis.normalize();
        // 计算旋转角度（两向量的夹角）
        const angle = Math.acos(yAxis.dot(direction));
        // 应用旋转
        cylinder.quaternion.setFromAxisAngle(rotationAxis, angle);
    }

    return cylinder;
}