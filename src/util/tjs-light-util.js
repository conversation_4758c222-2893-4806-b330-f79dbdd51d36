/**
 * 
 * @param {*} ctx 
 * @param {*} pointLight 
 */
export function tjs_createPointLightHelper(ctx, pointLight, opts = {}) {
    const { THREE } = ctx.imports;
    const pointLightHelper = new THREE.PointLightHelper(pointLight, 5);
    return pointLightHelper;

}

/**
 * 创建灯泡
 * @param {*} ctx 
 * @param {*} opts 
 * @returns 
 */
export function tjs_createBulb(ctx, opts = {}) {
    const { THREE } = ctx.imports;
    let { color, intensity, distance, decay } = opts;
    if (color == null) color = 0xffee88;
    if (intensity == null) intensity = 3;
    if (distance == null) distance = 0;
    if (decay == null) decay = 0.1;

    const bulbGeometry = new THREE.SphereGeometry(3, 16, 8);
    const bulbMat = new THREE.MeshStandardMaterial({
        emissive: 0xffffee,
        emissiveIntensity: 1,
        color: 0x000000
    });
    const mesh = new THREE.Mesh(bulbGeometry, bulbMat);
    mesh.name = 'bulbMesh';

    const bulbLight = new THREE.PointLight(color, intensity, distance, decay);
    bulbLight.name = 'bulbLight';
    bulbLight.castShadow = true;
    // 设置阴影精度
    bulbLight.shadow.mapSize.width = 1024 * 2;
    bulbLight.shadow.mapSize.height = 1024 * 2;
    // 设置阴影范围（关键）
    bulbLight.shadow.camera.near = 0.5;
    bulbLight.shadow.camera.far = 100000;
    
    mesh.add(bulbLight);

    return mesh;
}