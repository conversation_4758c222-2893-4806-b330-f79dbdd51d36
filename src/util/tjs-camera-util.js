/**
 * 获取相机朝向XY面的交叉点坐标
 * @param {*} ctx 上下文
 * @param {THREE.Camera} camera 
 * @param {*} y 固定值
 * @returns {[number, number, number]} 在XY面上的坐标，例：[12, 0, 66]
 */
export function tjs_getCameraToXZPos(ctx, camera, y) {
    const { THREE } = ctx.imports;
    if (y == null) y = ctx.data.designer.focusGroundHeight;

    // 创建一个射线，从相机位置指向相机的前方
    const direction = new THREE.Vector3();
    camera.getWorldDirection(direction);

    // 创建射线起点（相机位置）
    const origin = camera.position.clone();

    // 计算射线与XZ平面（y固定）的交点
    // 平面方程：y = 固定值
    // 射线方程：origin + t * direction
    // 求解t：origin.y + t * direction.y = 固定值
    // t = (固定值 - origin.y) / direction.y

    // 防止除以零（相机方向与XZ平面平行）
    if (Math.abs(direction.y) < 0.0001) {
        return [origin.x, y, origin.z]; // 返回相机在XZ平面上的投影点
    }

    // 计算t
    const t = (y - origin.y) / direction.y;

    // 如果t为负值，表示交点在相机后方
    if (t < 0) {
        return [origin.x, y, origin.z]; // 返回相机在XZ平面上的投影点
    }

    // 计算交点坐标
    const intersectX = origin.x + t * direction.x;
    const intersectZ = origin.z + t * direction.z;

    return [intersectX, y, intersectZ];
}

/**
 * 获取摄像机朝向前的一个点
 * @param obj 摄像机实例
 * @param distance 起始点到目标点的距离
 * @return {Vector3}
 */
export function tjs_calCameraLookAtPoint(ctx, camera, distance) {
    const { THREE } = ctx.imports;

    const direction = new THREE.Vector3(); // 创建一个向量用于表示摄像机的朝向
    camera.getWorldDirection(direction); // 获取摄像机的朝向
    const position = new THREE.Vector3(); // 创建一个向量用于表示摄像机的位置
    position.copy(camera.position); // 获取摄像机的位置
    direction.multiplyScalar(distance); // 将方向向量缩放为指定的距离
    position.add(direction); // 在摄像机位置上加上方向向量，得到目标点的位置
    return position; // 返回目标点的位置
}