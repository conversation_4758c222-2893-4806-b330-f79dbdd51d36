// 获取url参数
export function queryString(name, _window) {
    let t_window = null;
    if (_window != null) {
        t_window = _window;
    } else {
        t_window = window;
    }

    const reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
    const r = t_window.location.search.substr(1).match(reg);
    if (r != null)
        return r[2]; /*unescape(r[2]);*/
    return '';
}

// 清理html内容
export function clearHTML(val) {
    if (val != null) {
        const html = val.replace(/(<!--\s*(((?!<\/script>)(?:.|[\r\n]))*)\s*-->)|(<script>\s*(((?!<\/script>)(?:.|[\r\n]))*)\s*<\/script>)|(<style>\s*(((?!<\/style>)(?:.|[\r\n]))*)\s*<\/style>)|(<[^>]*>)|(&[^;]+;)/ig, '');
        return html;
    }
    return val;
}

// 是否手机端
export function isMobile() {
    if (navigator.userAgent.toLowerCase().indexOf("mobile") !== -1) {
        return true;
    }
    return false;
}


// 手动触发事件
export function touchEvent(tgt, eventName, win) {
    if (win == null) win = window;
    const ev = document.createEvent('MouseEvents');
    ev.initMouseEvent(eventName, true, true, win)
    tgt.dispatchEvent(ev);
}