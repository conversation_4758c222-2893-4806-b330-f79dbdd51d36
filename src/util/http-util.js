/**
 * 用POST发送json内容的http请求
 * 使用原生方法实现
 * @param {*} url 
 * @param {*} data 
 * @param {*} opts 
 */
export function reqPostJson(url, data, opts = {}) {
    const options = {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            ...opts.headers
        },
        body: JSON.stringify(data),
        ...opts
    };
    return fetch(url, options)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        });
}