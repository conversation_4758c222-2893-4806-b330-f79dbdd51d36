/**
 * 根据网格坐标获取真实坐标（此网格中心点坐标）
 * @param {*} eachGridSize 
 * @param {*} totalGridCount 
 * @param {*} gPos 
 * @returns {{x: number, y: number, z: number}}
 */
export function getXZRealPosByGridPos(eachGridSize, totalGridCount, gPos) {
    let x = 0, y = 0, z = 0;
    if (gPos instanceof Array) {
        x = gPos[0];
        z = gPos[2];
    } else {
        x = gPos.x;
        z = gPos.z;
    }

    // 将起点从左下角移动到中心位置（此时还是偏右上半格位置）
    let gPosX = x + totalGridCount / 2;
    let gPosZ = z + totalGridCount / 2;

    const realX = (gPosX - totalGridCount / 2) * eachGridSize + eachGridSize / 2;
    const realZ = (gPosZ - totalGridCount / 2) * eachGridSize + eachGridSize / 2;

    // 向左下移动半格
    return { x: realX - eachGridSize / 2, y: 0, z: realZ - eachGridSize / 2 };
}

/**
 * 根据真实坐标获取网格坐标
 * @param eachGridSize
 * @param totalGridCount
 * @param pos
 * @returns {{x: number, y: number, z: number}}
 */
export function getXZGridPosByRealPos(eachGridSize, totalGridCount, pos) {
    let x = 0, y = 0, z = 0;
    if (pos instanceof Array) {
        x = pos[0];
        z = pos[2];
    } else {
        x = pos.x;
        z = pos.z;
    }

    // 将真实坐标转换为左下角为起点的坐标系统
    const shiftedX = x + (totalGridCount / 2) * eachGridSize;
    const shiftedZ = z + (totalGridCount / 2) * eachGridSize;

    // 通过真实坐标计算网格坐标，并取整，使得网格坐标为整数
    const gPosX = Math.floor(shiftedX / eachGridSize) - Math.floor(totalGridCount / 2);
    const gPosZ = Math.floor(shiftedZ / eachGridSize) - Math.floor(totalGridCount / 2);

    return { x: gPosX, y: 0, z: gPosZ };
}

export function getXZGridRealPosByRealPos(eachGridSize, totalGridCount, pos) {
    const gPos = getXZGridPosByRealPos(eachGridSize, totalGridCount, pos);
    return getXZRealPosByGridPos(eachGridSize, totalGridCount, gPos);
}
