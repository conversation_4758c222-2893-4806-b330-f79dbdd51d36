
import { CGWorldModel } from "./model/CGWorldModel.js";
import { CGObjectModel } from "./model/CGObjectModel.js";
import { CG2DGridModel } from "./model/CG2DGridModel.js";
import { queryString } from "./util/web-util.js";
import { CGSkyModel } from "./model/CGSkyModel.js";
import { CGLightModel } from "./model/CGLightModel.js";
import { getXZGridPosByRealPos, getXZRealPosByGridPos, getXZGridRealPosByRealPos } from "./util/game-grid-util.js";

import { MoveSelectedCGObjectAbility } from "./ability/MoveSelectedCGObjectAbility.js";
import { FirstPersonControlsAbility } from "./ability/FirstPersonControlsAbility.js";
import { SelectCGObjectAbility } from "./ability/SelectCGObjectAbility.js";
import { UserControlsAbility } from "./ability/UserControlsAbility.js";
import { GetUserChangeAnyEventAbility } from "./ability/GetUserChangeAnyEventAbility.js";


import { ProjectStorageMapper } from "./mapper/ProjectStorageMapper.js";
import { ModelsStorageMapper } from "./mapper/ModelsStorageMapper.js";
import { ToolBarViewModel } from "./view-model/ToolBarViewModel.js";
import { PropBarViewModel } from "./view-model/PropBarViewModel.js";
import { CGWallModel } from "./model/CGWallModel.js";
import { ProjectStorageService } from "./service/ProjectStorageService.js";
import { CGPlayerStartModel } from "./model/CGPlayerStartModel.js";
import { CGPaintingFrameModel } from "./model/CGPaintingFrameModel.js";
import { CGBulbModel } from "./model/CGBulbModel.js";
import { CGFloorModel } from "./model/CGFloorModel.js";
import { CGHUDAxesIndicator } from "./model/CGHUDAxesIndicator.js";
import { CGWallMountedTelevisionModel } from "./model/CGWallMountedTelevisionModel.js";
import {SandboxConnection} from "./service/SandboxConnection";

export function start(imports, opts = {}) {
    const {
        THREE,
        CSS2DRenderer,
        CSS2DObject,
        CANNON,
        CannonDebugger,
    } = imports;

    // 上下文
    const ctx = {
        imports: imports,
        gameType: opts.gameType,
        canAutoSaveProject: opts.canAutoSaveProject,
        autoLoadProject: opts.autoLoadProject,

        isReady: false,
        // 调试
        debug: false,
        // 启用开发者视口
        enableDevViewport: false,
        // 工具库
        utils: {},

        // 默认场景
        scene: null,
        // 默认相机（游戏正式运行时主相机，非开发者视口的相机）
        camera: null,
        renderer: null,
        css2dRenderer: null,


        prevPerformaceTime: performance.now(),
        // 渲染两帧画面之间所经过的时间
        deltaTime: null,

        // 游戏世界
        cgWorld: null,

        // 物理引擎
        physWorld: null,
        physicsBodies: [],

        abilityTypes: {
            MoveSelectedCGObjectAbility, FirstPersonControlsAbility, SelectCGObjectAbility,
            UserControlsAbility, GetUserChangeAnyEventAbility,
        },
        objectTypes: {
            CGObjectModel, CGWallModel, CGPlayerStartModel, CGPaintingFrameModel, CGBulbModel, CGFloorModel,
            CGHUDAxesIndicator, CGWallMountedTelevisionModel,
        },

        data: {
            showFPS: false,
            showMemory: false,
            // 能否暴露上下文
            canExposeContext: false,

            // 当前相机（用于设计师加载项目时保持镜头）
            camera: {
            },
            // 项目
            project: {
                id: 'unknown',
                name: '未知项目',
            },
            // 世界
            world: {
                // 一个单位的大小（可以等价于一米的长度是多少）
                unitSize: 50,
                // 重力大小
                gravityUnit: 3,
            },
            // 玩家
            player: {
                startPosUnit: [0, 1.7, 0],
                startLookAtPosUnit: [1, 0, 0],
                heightUnit: 1.7,
                moveSpeedUnit: 0.3,
                fastMoveSpeedUnit: 0.6,
                jumpStrengthUnit: 1,

                // （调试）显示中心坐标指示器
                showCenterIndicator: false,
            },
            // 设计师
            designer: {
                // 自动保存项目
                autoSaveProject: true,
                // 专注的地面高度
                focusGroundHeight: 0,
                // 磁吸网格大小（米），用于摆放物体时定位
                magneticGridSizeUnit: 0.1,
                // 地面网格
                groundGrid: {
                    eachGridSize: 5,
                    totalGridCount: 11,
                },
            },
        },
    };

    ctx.projectStorageMapper = new ProjectStorageMapper(ctx);
    ctx.modelsStorageMapper = new ModelsStorageMapper(ctx);
    ctx.projectStorageService = new ProjectStorageService(ctx);

    // 动画
    function animate() {
        requestAnimationFrame(animate);

        const time = performance.now();
        const delta = (time - ctx.prevPerformaceTime) / 1000;
        ctx.deltaTime = delta;

        // 更新物理引擎
        if (ctx.physWorld) {
            // 步长，越大移动越快
            ctx.physWorld.step(1 / 30);

            // 同步物理体和Three.js对象
            ctx.physicsBodies.forEach(({ mesh, body }) => {
                mesh.position.copy(body.position);
                mesh.quaternion.copy(body.quaternion);
            });

        }
        if (ctx.cgWorld) {
            ctx.cgWorld.update({});
        }
        if (ctx.physWorldDebugger) {
            ctx.physWorldDebugger.update();
        }

        ctx.renderer.render(ctx.scene, ctx.camera);
        ctx.css2dRenderer.render(ctx.scene, ctx.camera);

        ctx.prevPerformaceTime = time;
    }

    // 初始化上下文
    async function initContext(ctx) {
        // 输出开发者提示，例如：项目保存成功
        ctx.utils.outputDevTip = function (msg) {
            if (ctx.propBarViewModel) {
                ctx.propBarViewModel.showMessage('保存项目成功');
            }
            if (ctx.sandboxConnection) {
                ctx.sandboxConnection.showDevTip(msg);
            }
        };

        // 根据标准单位获取游戏内大小
        ctx.utils.getRealSize = function (sizeUnit) {
            return ctx.data.world.unitSize * sizeUnit;
        };
        ctx.utils.getRealSizeList = function (sizeUnitList) {
            return sizeUnitList.map((n) => {
                return ctx.data.world.unitSize * n;
            });
        };
        ctx.utils.getUnitSize = function (realSize) {
            return realSize / ctx.data.world.unitSize;
        };
        ctx.utils.getUnitSizeList = function (realSizeList) {
            return realSizeList.map((n) => {
                return n / ctx.data.world.unitSize;
            });
        };


        ctx.utils.getDesignGroundRealPosByGridPos = function (gPos) {
            return getXZRealPosByGridPos(ctx.data.designer.groundGrid.eachGridSize,
                ctx.data.designer.groundGrid.totalGridCount, gPos);
        };
        ctx.utils.getDesignGroundGridPosByRealPos = function (pos) {
            return getXZGridPosByRealPos(ctx.data.designer.groundGrid.eachGridSize,
                ctx.data.designer.groundGrid.totalGridCount, pos);
        };
        ctx.utils.getDesignGroundGridRealPosByRealPos = function (pos) {
            return getXZGridRealPosByRealPos(ctx.data.designer.groundGrid.eachGridSize,
                ctx.data.designer.groundGrid.totalGridCount, pos);
        };

        ctx.utils.showFPSMonitor = function () {
            (function () {
                var script = document.createElement('script');
                script.onload = function () {
                    var stats = new Stats();
                    document.body.appendChild(stats.dom);
                    stats.dom.style.left = '350px'
                    requestAnimationFrame(function loop() {
                        stats.update();
                        requestAnimationFrame(loop)
                    });
                };
                script.src = './lib/stats/stats.min.js';
                document.head.appendChild(script);
            })()
        };

        ctx.utils.showMemoryMonitor = function () {
            (function () {
                var script = document.createElement('script');
                script.src = './lib/memory-stats/memory-stats-start.js';
                document.head.appendChild(script);
            })()
        };
    }

    // 初始化图形引擎
    async function initGraphicsEngine(ctx) {
        const scene = new THREE.Scene();

        // const camera = new THREE.OrthographicCamera(-400, 400, 300, -300, 1, 500); // 创建正交相机（2d）
        const camera = new THREE.PerspectiveCamera(50, window.innerWidth / window.innerHeight, 0.1, 100000); // 创建透视相机（3d）
        camera.position.set(0, 50, 0); // 调整相机高度
        camera.lookAt(90, 30, 0);

        const renderer = new THREE.WebGLRenderer({ canvas: document.getElementById('gameCanvas') });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.shadowMap.enabled = true;
        renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        document.body.appendChild(renderer.domElement);

        const css2dRenderer = new CSS2DRenderer();
        css2dRenderer.setSize(window.innerWidth, window.innerHeight);
        css2dRenderer.domElement.style.position = 'absolute';
        css2dRenderer.domElement.style.top = '0px';
        css2dRenderer.domElement.style.pointerEvents = 'none'; // 解决事件被阻挡导致无法旋转相机的问题
        // css2dRenderer.domElement.style.zIndex = "2";
        document.body.appendChild(css2dRenderer.domElement);

        ctx.scene = scene;
        ctx.camera = camera;
        ctx.renderer = renderer;
        ctx.css2dRenderer = css2dRenderer;

        // 自动适应窗口大小
        window.addEventListener('resize', () => {
            ctx.cgWorld.resize();

            ctx.camera.aspect = window.innerWidth / window.innerHeight;
            ctx.camera.updateProjectionMatrix();
            ctx.renderer.setSize(window.innerWidth, window.innerHeight);
            ctx.css2dRenderer.setSize(window.innerWidth, window.innerHeight);
        });
    }

    // 初始化物理引擎
    async function initPhysicsEngine(ctx) {
        // 创建游戏世界
        ctx.cgWorld = new CGWorldModel(ctx);
        // 创建物理世界
        ctx.physWorld = new CANNON.World();
        ctx.physWorld.gravity.set(0, -9.82, 0); // 设置重力
        ctx.physWorld.broadphase = new CANNON.NaiveBroadphase();

        // if (ctx.enableDevViewport) {
        //     ctx.physWorldDebugger = new CannonDebugger(ctx.scene, ctx.physWorld, {});
        // }
    }

    async function main(ctx) {
        if (queryString('debug') === '1') {
            ctx.debug = true;
        }
        if (opts.dev || queryString('dev') === '1') {
            ctx.enableDevViewport = true;
            ctx.sandboxConnection = new SandboxConnection(ctx);
            window.sandboxConnection = ctx.sandboxConnection;
        }
        const t_project_id = queryString('pjt');
        if (t_project_id && t_project_id.length > 0) {
            ctx.data.project.id = t_project_id;
        }

        if (ctx.autoLoadProject !== false) {
            // 加载项目
            await ctx.projectStorageService.load(opts);
        }

        if (opts.beforeMainStartItems) {
            for (const item of opts.beforeMainStartItems) {
                if (item.onlyDevViewport && !ctx.enableDevViewport) {
                }
                else if (item.onlyViewport && ctx.enableDevViewport) {
                }
                else if (item.type === 'ctx-init') {
                    for (const prop in item.data) {
                        ctx[prop] = item.data[prop];
                    }
                }
                else if (item.type === 'camera-init') {
                    for (const prop in item.data) {
                        ctx.data.camera[prop] = item.data[prop];
                    }
                }
                else if (item.type === 'player-init') {
                    for (const prop in item.data) {
                        ctx.data.player[prop] = item.data[prop];
                    }
                }
                else if (item.type === 'world-init') {
                    if (item.data.unitSize >= 0) ctx.data.world.unitSize = item.data.unitSize;
                    if (item.data.gravityUnit >= 0) ctx.data.world.gravityUnit = item.data.gravityUnit;
                }
            }
        }

        // *** 初始上下文 ***
        await initContext(ctx);

        // *** 初始化图形引擎 ***
        await initGraphicsEngine(ctx);

        // *** 初始化物理引擎 ***
        await initPhysicsEngine(ctx);



        if (opts.afterMainStartItems) {
            for (const item of opts.afterMainStartItems) {
                if (item.onlyDevViewport && !ctx.enableDevViewport) {
                }
                else if (item.data.__onlyDevViewport && !ctx.enableDevViewport) {
                }
                else if (item.onlyViewport && ctx.enableDevViewport) {
                }
                else if (item.data.__onlyViewport && ctx.enableDevViewport) {
                }
                else if (item.type === 'world-grid-build') {
                    ctx.data.designer.groundGrid.eachGridSize = ctx.utils.getRealSize(item.data.eachGridSizeUnit);
                    ctx.data.designer.groundGrid.totalGridCount = item.data.totalGridCount;
                    await ctx.cgWorld.addObject(new CG2DGridModel(ctx, item.data));
                }
                else if (item.type === 'object-build') {
                    await ctx.cgWorld.addObject(new CGObjectModel(ctx, item.data));
                }
                else if (item.type === 'sky-object-build') {
                    await ctx.cgWorld.addObject(new CGSkyModel(ctx, item.data));
                }
                else if (item.type === 'light-object-build') {
                    await ctx.cgWorld.addObject(new CGLightModel(ctx, item.data));
                }
                else if (item.type.endsWith('-ability-build')) {
                    let typeName = item.type.replace('-ability-build', '');
                    if (ctx.abilityTypes[typeName] == null) {
                        alert(`未找到能力类型：${typeName}`);
                    }
                    else {
                        await ctx.cgWorld.addAbility(new ctx.abilityTypes[typeName](ctx, item.data), item.name);
                    }
                }
                else if (item.type.endsWith('-object-build')) {
                    let typeName = item.type.replace('-object-build', '');
                    if (ctx.objectTypes[typeName] == null) {
                        alert(`未找到对象类型：${typeName}`);
                    }
                    else {
                        await ctx.cgWorld.addObject(new ctx.objectTypes[typeName](ctx, item.data));
                    }
                }
            }
        }

        await ctx.cgWorld.startAbilites();
        await ctx.cgWorld.buildObjects();

        if (ctx.canExposeContext) {
            window.ctx = ctx;
        }
        // 显示实时FPS数据
        if (ctx.showFPS) {
            ctx.utils.showMemoryMonitor()
        }
        // 显示实时内存数据
        if (ctx.showMemory) {
            ctx.utils.showMemoryMonitor()
        }

        // 开发视图
        if (ctx.enableDevViewport) {
            // 实时坐标指示器
            const topAxesIndicator = new CGHUDAxesIndicator(ctx, {
                __autoSave: false,
            });
            await ctx.cgWorld.addObjectAndBuild(topAxesIndicator);

            // 创建工具栏、属性栏
            if (opts.editor) {
                ctx.toolBarViewModel = new ToolBarViewModel(ctx);
                ctx.toolBarViewModel.build();
                ctx.propBarViewModel = new PropBarViewModel(ctx);
                ctx.propBarViewModel.build();
            }
        }

        ctx.isReady = true;

        console.log(`玩家身高：${ctx.data.player.heightUnit}`);
        console.log(`玩家移动速度：${ctx.data.player.moveSpeedUnit}`);

        animate();
    }

    main(ctx);
}

global.start = start;