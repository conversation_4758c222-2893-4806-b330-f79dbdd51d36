/**
 * 开发者视口服务
 * 描述：以开发视角来观察游戏场景，类似游戏IDE开发工具
 * 待解决问题：FPV模式下没法旋转和移动同时进行
 */
export class DevViewportService {

    constructor(ctx, cfg) {
        this.ctx = ctx;
        this.cfg = cfg;

        if (this.cfg.debug == null) this.cfg.debug = false;
        // 游戏默认相机（游戏运行时主相机）
        if (this.cfg.defCamera == null) this.cfg.defCamera = null;
        // 每网格大小
        if (this.cfg.eachGridSize == null) this.cfg.eachGridSize = 10;
        // 总网格数
        if (this.cfg.totalGridCount == null) this.cfg.totalGridCount = 100;
        // 中心点坐标指示器尺寸
        if (this.cfg.caiAxesSize == null) this.cfg.caiAxesSize = 200;
        if (this.cfg.caiArrowRadius == null) this.cfg.caiArrowRadius = 1;

        if (this.cfg.devCameraPos == null) this.cfg.devCameraPos = { x: 100, y: 0, z: 300 };

        if (this.cfg.devCameraFPVMoveSpeed == null) this.cfg.devCameraFPVMoveSpeed = 3.5;
        if (this.cfg.devCameraFPVLeftRightTopDownMoveSpeed == null) this.cfg.devCameraFPVLeftRightTopDownMoveSpeed = 10;
        if (this.cfg.devCameraFPVRotateSpeed == null) this.cfg.devCameraFPVRotateSpeed = 0.0005;

        this._devCameraFPVMemory = {};
    }

    start() {
        this.buildGridHelper();
        this.buildCenterAxesIndicator();
        this.buildDevCamera();
        this.buildDevCameraFPV();
        this.buildDefCameraHelper();
        this.buildLightHelper();
        // this.buildObjectPropertyEditorBar();
    }

    resize() {
    }

    animate() {
        const { THREE } = this.ctx.imports;

        // FPV视角移动
        let moveFrontSpeed = this.cfg.devCameraFPVMoveSpeed;
        let moveBackSpeed = this.cfg.devCameraFPVMoveSpeed;
        let moveLeftSpeed = this.cfg.devCameraFPVLeftRightTopDownMoveSpeed;
        let moveRightSpeed = this.cfg.devCameraFPVLeftRightTopDownMoveSpeed;
        let moveUpSpeed = this.cfg.devCameraFPVLeftRightTopDownMoveSpeed;
        let moveDownSpeed = this.cfg.devCameraFPVLeftRightTopDownMoveSpeed;

        if (this._devCameraFPVMemory.startMoveFrontTime != null && this._devCameraFPVMemory.startMoveFrontTime > 0) {
            const wd = new THREE.Vector3();
            // 获取朝向
            this._devCamera.getWorldDirection(wd);
            // 修正朝向为水平向前
            wd.y = 0;
            this._devCamera.position.add(wd.multiplyScalar(moveFrontSpeed));
        }
        else if (this._devCameraFPVMemory.startMoveBackTime != null && this._devCameraFPVMemory.startMoveBackTime > 0) {
            const wd = new THREE.Vector3();
            // 获取朝向
            this._devCamera.getWorldDirection(wd);
            // 修正朝向为水平向前
            wd.y = 0;
            this._devCamera.position.add(wd.multiplyScalar(-moveBackSpeed));
        }
        else if (this._devCameraFPVMemory.startMoveLeftTime != null && this._devCameraFPVMemory.startMoveLeftTime > 0) {
            const ld = new THREE.Vector3(-0.3, 0, 0);
            // 向左
            this._devCamera.translateOnAxis(ld, moveLeftSpeed);
        }
        else if (this._devCameraFPVMemory.startMoveRightTime != null && this._devCameraFPVMemory.startMoveRightTime > 0) {
            const ld = new THREE.Vector3(0.3, 0, 0);
            // 向右
            this._devCamera.translateOnAxis(ld, moveRightSpeed);
        }
        else if (this._devCameraFPVMemory.startMoveUpTime != null && this._devCameraFPVMemory.startMoveUpTime > 0) {
            const ld = new THREE.Vector3(0, 0.3, 0);
            // 向上
            this._devCamera.translateOnAxis(ld, moveUpSpeed);
        }
        else if (this._devCameraFPVMemory.startMoveDownTime != null && this._devCameraFPVMemory.startMoveDownTime > 0) {
            const ld = new THREE.Vector3(0, -0.3, 0);
            // 向下
            this._devCamera.translateOnAxis(ld, moveDownSpeed);
        }
    }

    /**
     * 构建网格辅助
     */
    buildGridHelper() {
        const { THREE } = this.ctx.imports;

        let eachGridSize = this.cfg.eachGridSize;
        let totalGridCount = this.cfg.totalGridCount;

        const gridHelper = new THREE.GridHelper(eachGridSize * totalGridCount, totalGridCount,
            'silver', 'silver');
        // 旋转网格使其与X-Y平面对齐
        // gridHelper.rotation.x = Math.PI / 2;

        // this.ctx.scene.add(gridHelper);
    }

    /**
     * 构建中心点坐标指示器
     */
    buildCenterAxesIndicator() {
        const { CSS2DObject, THREE } = this.ctx.imports;

        let arrowLabelDistance = 5;

        const axesHelper = new THREE.AxesHelper(this.cfg.caiAxesSize);
        axesHelper.renderOrder = 1;
        axesHelper.position.set(0, 0, 0);

        {
            // x轴箭头（红色）
            const geometry = new THREE.ConeGeometry(this.cfg.caiArrowRadius, this.cfg.caiAxesSize / 5, 32);
            const material = new THREE.MeshBasicMaterial({color: 'red'});
            const cone = new THREE.Mesh(geometry, material);
            cone.position.set(this.cfg.caiAxesSize, 0, 0);
            cone.rotateZ(-Math.PI / 180 * 90);
            axesHelper.add(cone);

            // x轴标签
            const labelDiv = document.createElement('div');
            labelDiv.textContent = '+X';
            labelDiv.style = 'color:silver;';
            const css2dLabel = new CSS2DObject(labelDiv);
            css2dLabel.position.set(this.cfg.caiAxesSize + arrowLabelDistance, 0, 0);
            axesHelper.add(css2dLabel);
        }
        {
            // y轴箭头（绿色）
            const geometry = new THREE.ConeGeometry(this.cfg.caiArrowRadius, this.cfg.caiAxesSize / 5, 32);
            const material = new THREE.MeshBasicMaterial({color: 'green'});
            const cone = new THREE.Mesh(geometry, material);
            cone.position.set(0, this.cfg.caiAxesSize, 0);
            axesHelper.add(cone);

            // y轴标签
            const labelDiv = document.createElement('div');
            labelDiv.textContent = '+Y';
            labelDiv.style = 'color:silver;';
            const css2dLabel = new CSS2DObject(labelDiv);
            css2dLabel.position.set(0, this.cfg.caiAxesSize + arrowLabelDistance, 0);
            axesHelper.add(css2dLabel);
        }
        {
            // z轴箭头（蓝色）
            const geometry = new THREE.ConeGeometry(this.cfg.caiArrowRadius, this.cfg.caiAxesSize / 5, 32);
            const material = new THREE.MeshBasicMaterial({color: 'blue'});
            const cone = new THREE.Mesh(geometry, material);
            cone.position.set(0, 0, this.cfg.caiAxesSize);
            cone.rotateX(Math.PI / 180 * 90);
            axesHelper.add(cone);

            // z轴标签
            const labelDiv = document.createElement('div');
            labelDiv.textContent = '+Z';
            labelDiv.style = 'color:silver;';
            const css2dLabel = new CSS2DObject(labelDiv);
            css2dLabel.position.set(0, 0, this.cfg.caiAxesSize + arrowLabelDistance);
            axesHelper.add(css2dLabel);
        }

        this.ctx.scene.add(axesHelper);
    }

    /**
     * 构建开发者相机
     */
    buildDevCamera() {
        const { THREE } = this.ctx.imports;

        if (this._devCamera == null) {
            const fov = 50; // 视角
            const aspect = window.innerWidth / window.innerHeight; // 宽高比
            const near = 0.01; // 近剪裁面
            const far = 3000; // 远剪裁面
            this._devCamera = new THREE.PerspectiveCamera(fov, aspect, near, far);
            this._devCamera.position.set(this.cfg.devCameraPos.x, this.cfg.devCameraPos.y, this.cfg.devCameraPos.z);
            this._devCamera.lookAt(0, 0, 0);
        }
        this.ctx.camera = this._devCamera;
    }

    /**
     * 构建开发者相机第一人称视角控制
     * 按住鼠标旋转视角，ASWD来平移或前进后退，按空格向上移动，按Shift向下移动
     */
    buildDevCameraFPV() {
        const self = this;
        const { THREE } = this.ctx.imports;

        if (self.ctx.userControllerService) {
            // 转向模式：1 与鼠标反向转，-1 与鼠标正向转
            let rotateMode = -1;

            self.ctx.userControllerService.setUserEvent('dev_camera_fpv_mousedown', ['mousedown', 'touchstart'], (e) => {
                const now = new Date().getTime();

                // 触摸模式
                if (e.touches != null && e.touches.length > 0)  {
                    if (self._devCameraFPVMemory.mouseupTime == null || now - self._devCameraFPVMemory.mouseupTime > 500) {
                        self._devCameraFPVMemory.mousedown = true;
                        self._devCameraFPVMemory.mousedownTime = now;
                        if (self.cfg.debug) console.log(`${e.type} 【触摸】 鼠标按下`);

                        if (e.touches != null && e.touches.length > 0) {
                            const touch = e.touches[0];
                            self._devCameraFPVMemory.touchStartX = touch.clientX;
                            self._devCameraFPVMemory.touchStartY = touch.clientY;
                        }
                    }
                }
                // 按键模式
                else {
                    self._devCameraFPVMemory.mousedown = true;
                    self._devCameraFPVMemory.mousedownTime = now;
                    if (self.cfg.debug) console.log(`${e.type} 【按键】 鼠标按下`);

                    // console.log(`pageX: ${e.pageX}, screenX: ${e.screenX}`);
                    self._devCameraFPVMemory.mousedownX = e.screenX;
                    self._devCameraFPVMemory.mousedownY = e.screenY;
                }

            });

            // 控制镜头旋转
            self.ctx.userControllerService.setUserEvent('dev_camera_fpv_mousemove', ['mousemove', 'touchmove'], (e) => {
                if (self._devCameraFPVMemory.mousedown) {
                    const rotateSpeed = self.cfg.devCameraFPVRotateSpeed;
                    // *** 左右转向 ***
                    let movementX = 0;
                    let movementY = 0;

                    // 触摸模式
                    if (e.touches != null && e.touches.length > 0) {
                        if (self.cfg.debug) console.log(`${e.type} 【触摸】 鼠标移动`);
                        const touch = e.touches[0];

                        // 使用相对位移而不是累积位移
                        movementX = touch.clientX - (self._devCameraFPVMemory.touchCurrentX || self._devCameraFPVMemory.touchStartX);
                        movementY = touch.clientY - (self._devCameraFPVMemory.touchCurrentY || self._devCameraFPVMemory.touchStartY);
                        
                        // 更新当前位置
                        self._devCameraFPVMemory.touchCurrentX = touch.clientX;
                        self._devCameraFPVMemory.touchCurrentY = touch.clientY;
                    }
                    // 按键模式
                    else {
                        if (self.cfg.debug) console.log(`${e.type} 【按键】 鼠标移动`);
                        // 使用相对位移而不是累积位移
                        movementX = e.screenX - (self._devCameraFPVMemory.lastMouseX || self._devCameraFPVMemory.mousedownX);
                        movementY = e.screenY - (self._devCameraFPVMemory.lastMouseY || self._devCameraFPVMemory.mousedownY);
                        
                        // 更新上一次鼠标位置
                        self._devCameraFPVMemory.lastMouseX = e.screenX;
                        self._devCameraFPVMemory.lastMouseY = e.screenY;
                    }

                    // 限制旋转速度，避免突然加速
                    const maxMovement = 50; // 限制单次最大移动量
                    movementX = Math.max(-maxMovement, Math.min(maxMovement, movementX));
                    movementY = Math.max(-maxMovement, Math.min(maxMovement, movementY));

                    // 左右旋转根据世界坐标轴的y轴
                    self._devCamera.rotateOnWorldAxis(new THREE.Vector3(0, 1, 0), movementX * rotateSpeed * rotateMode);
                    // 上下旋转根据自身的x轴（物体朝向可能是y轴正向）
                    // self._devCamera.rotateOnWorldAxis(new THREE.Vector3(1, 0, 0), e.movementY * rotateSpeed * rotateMode);
                    self._devCamera.rotateOnAxis(new THREE.Vector3(1, 0, 0), movementY * rotateSpeed * rotateMode);
                }
            });
            self.ctx.userControllerService.setUserEvent('dev_camera_fpv_mouseup', ['mouseup', 'touchend'], (e) => {
                self._devCameraFPVMemory.mousedown = false;
                self._devCameraFPVMemory.mouseupTime = new Date().getTime();
                // 重置鼠标位置记录，避免下次按下时产生跳跃
                self._devCameraFPVMemory.lastMouseX = null;
                self._devCameraFPVMemory.lastMouseY = null;
                self._devCameraFPVMemory.touchCurrentX = null;
                self._devCameraFPVMemory.touchCurrentY = null;
                if (self.cfg.debug) console.log(`${e.type} 鼠标抬起`);
            });

            // 控制镜头移动
            self.ctx.userControllerService.setUserEvent('dev_camera_fpv_keydown', ['keydown'], (e) => {
                // 87 w 前进，65 a 左，68 d 右，83 s 后退
                let time = self.ctx.time;
                if (time == null) time = new Date().getTime();

                if (self._devCameraFPVMemory.mousedown) {
                    if (e.keyCode == 87) {
                        self._devCameraFPVMemory.startMoveFrontTime = time;
                    } else if (e.keyCode == 83) {
                        self._devCameraFPVMemory.startMoveBackTime = time;
                    } else if (e.keyCode == 65) {
                        self._devCameraFPVMemory.startMoveLeftTime = time;
                    } else if (e.keyCode == 68) {
                        self._devCameraFPVMemory.startMoveRightTime = time;
                    } else if (e.keyCode == 32) {
                        self._devCameraFPVMemory.startMoveUpTime = time;
                    } else if (e.keyCode == 16) {
                        self._devCameraFPVMemory.startMoveDownTime = time;
                    }
                }

                if (self._devCameraFPVMemory.touchCurrentX != null) {
                    self._devCameraFPVMemory.touchStartX = self._devCameraFPVMemory.touchCurrentX;
                }
                if (self._devCameraFPVMemory.touchCurrentY != null) {
                    self._devCameraFPVMemory.touchStartY = self._devCameraFPVMemory.touchCurrentY;
                }
            });

            self.ctx.userControllerService.setUserEvent('dev_camera_fpv_keyup', ['keyup'], (e) => {
                // 87 w 前进，65 a 左，68 d 右，83 s 后退，32 空格 向上，16 shift 向下
                if (self.cfg.debug) console.log(`${e.keyCode} 键盘抬起`)
                if (e.keyCode == 87) {
                    self._devCameraFPVMemory.startMoveFrontTime = 0;
                }
                else if (e.keyCode == 83) {
                    self._devCameraFPVMemory.startMoveBackTime = 0;
                }
                else if (e.keyCode == 65) {
                    self._devCameraFPVMemory.startMoveLeftTime = 0;
                }
                else if (e.keyCode == 68) {
                    self._devCameraFPVMemory.startMoveRightTime = 0;
                }
                else if (e.keyCode == 32) {
                    self._devCameraFPVMemory.startMoveUpTime = 0;
                }
                else if (e.keyCode == 16) {
                    self._devCameraFPVMemory.startMoveDownTime = 0;
                }
            });
        }
    }

    /**
     * 构建默认相机辅助
     */
    buildDefCameraHelper() {
        const { THREE } = this.ctx.imports;

        this._defCameraHelper = new THREE.CameraHelper(this.cfg.defCamera);
        this.ctx.scene.add(this._defCameraHelper);
    }

    /**
     * 构建光源辅助
     */
    buildLightHelper() {
        const { THREE } = this.ctx.imports;

        // 点光源辅助
        if (this.ctx.pointLight) {
            const pointLightHelper = new THREE.PointLightHelper(this.ctx.pointLight, 5); // 1为助手的大小
            this.ctx.scene.add(pointLightHelper);
        }
        // 方向光源辅助
        if (this.ctx.directionalLight) {
            const directionalLightHelper = new THREE.DirectionalLightHelper(this.ctx.directionalLight, 5, 0xff0000); // 创建可视化平行光的辅助对象
            this.ctx.scene.add(directionalLightHelper);
        }
    }

    /**
     * 构建对象属性编辑栏（网页界面）
     */
    buildObjectPropertyEditorBar() {
        const el = document.getElementById('dev-viewport-area');
        el.style.display = '';
        // const css2dObj = new CSS2DObject(el);
        // this.ctx.scene.add(css2dObj);
    }
}