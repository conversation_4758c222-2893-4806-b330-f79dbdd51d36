import {CGSkyModel} from "../model/CGSkyModel";
import {FirstPersonControlsAbility} from "../ability/FirstPersonControlsAbility";
import {UserControlsAbility} from "../ability/UserControlsAbility";
import {GetUserChangeAnyEventAbility} from "../ability/GetUserChangeAnyEventAbility";
import {CG2DGridModel} from "../model/CG2DGridModel";

/**
 * 沙盒连接，用于给编辑器访问沙盒
 */
export class SandboxConnection {

    constructor(ctx) {
        this.ctx = ctx;

        this._devTipEventListeners = [];
    }

    /**
     * 初始化
     * @param data
     * @returns {Promise<void>}
     */
    async init(data = {}) {
        if (data.projectId != null) {
            this.ctx.data.project.id = data.projectId;
        }
        if (data.projectName != null) {
            this.ctx.data.project.name = data.projectName;
        }

        if (data.showFPS != null) {
            this.ctx.showFPS = data.showFPS;
            this.ctx.utils.showFPSMonitor();
        }
        if (data.showMemory != null) {
            this.ctx.showMemory = data.showMemory;
            this.ctx.utils.showMemoryMonitor();
        }

        return {

        }
    }

    /**
     * 开启天空盒子
     * @param data
     * @returns {Promise<void>}
     */
    async openSkyBox(data = {}) {
        await this.ctx.cgWorld.addObject(new CGSkyModel(this.ctx, data));
    }

    /**
     * 开启开发者相机控制
     * @param data
     * @returns {Promise<void>}
     */
    async openDevCameraControls(data = {}) {
        await this.ctx.cgWorld.addAbility(new FirstPersonControlsAbility(this.ctx, data), 'first_person_controls')
    }

    /**
     * 开启开发者网格
     * @param data
     * @returns {Promise<void>}
     */
    async openDevGrid(data = {}) {
        this.ctx.data.designer.groundGrid.eachGridSize = this.ctx.utils.getRealSize(data.eachGridSizeUnit);
        this.ctx.data.designer.groundGrid.totalGridCount = data.totalGridCount;
        await this.ctx.cgWorld.addObject(new CG2DGridModel(this.ctx, data));
    }

    /**
     * 添加开发者提示事件监听
     * @param func
     */
    addDevTipEventListener(func) {
        this._devTipEventListeners.push({
            func,
        });
    }

    async build() {
        await this.ctx.cgWorld.addAbility(new UserControlsAbility(this.ctx, {}), 'user_ctrl');
        await this.ctx.cgWorld.addAbility(new GetUserChangeAnyEventAbility(this.ctx, {}), 'get_user_change_any_event');

        await this.ctx.cgWorld.startAbilites();
        await this.ctx.cgWorld.buildObjects();
    }

    isReady() {
        return this.ctx.isReady;
    }

    /**
     * 输出开发者提示，例如：项目保存成功
     * @param msg
     */
    showDevTip(msg) {
        this._devTipEventListeners.forEach((n) => {
            n.func(msg);
        })
    }
}