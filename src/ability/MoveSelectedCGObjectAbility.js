import { CGObject } from "../model/CGObject";
import { getXZGridRealPosByRealPos } from "../util/game-grid-util";

/**
 * 移动选中的游戏对象能力
 * 拖动对象在xz轴上移动
 */
export class MoveSelectedCGObjectAbility extends CGObject {

    /**
     * 
     * @param {*} ctx 
     * @param {*} data 
     */
    constructor(ctx, data) {
        super(ctx, data);
    }

    /**
     * 初始化
     * @param {*} pars 
     */
    init(pars) {

    }

    /**
     * 启动
     */
    start(pars) {
        const self = this;
        const { cgWorld } = pars;
        const { THREE } = this.ctx.imports;
        const userControlsAbility = cgWorld.getAbility('user_ctrl');

        // 鼠标按下时的时间戳
        this.startTime = 0;
        // 鼠标抬起时的时间戳
        this.endTime = 0;
        // 鼠标移动时间
        this.moveTime = 0;
        // 鼠标按下时的坐标
        this.mouse_2d_pos_start = new THREE.Vector2();
        // 鼠标移动时的坐标
        this.mouse_2d_pos_move = new THREE.Vector2();
        // 鼠标按下时的在三维坐标
        this.mouse_3d_pos_start;
        // 鼠标按下时的对象三维坐标
        this.obj_pos_start;

        this.is_in_select_mode = false;

        /**
         * 拦截鼠标或触摸事件
         * @param {MouseEvent|TouchEvent} e
         */
        userControlsAbility.setUserEvent('move-selected-cgo',
            ["mouseleftdown", "mouseup", "mousemove", "touchstart", "touchend", "touchmove"], (e, type) => {
                // 鼠标按下
                if (type.startsWith('mouseleftdown')) {
                    // 将鼠标位置转换为标准化设备坐标（NDC）
                    self.mouse_2d_pos_start.x = (e.clientX / window.innerWidth) * 2 - 1;
                    self.mouse_2d_pos_start.y = -(e.clientY / window.innerHeight) * 2 + 1;
                }
                else if (type.startsWith('touchstart')) {
                    const touch = e.touches[0];
                    // 将触摸位置转换为标准化设备坐标（NDC）
                    self.mouse_2d_pos_start.x = (touch.clientX / window.innerWidth) * 2 - 1;
                    self.mouse_2d_pos_start.y = -(touch.clientY / window.innerHeight) * 2 + 1;
                }

                // 鼠标抬起
                if (type.startsWith('mouseup') || type.startsWith('touchend')) {
                    self.endTime = Date.now();
                    // 恢复视角控制
                    const fpc = self.ctx.cgWorld.getAbility('first_person_controls');
                    fpc.active();
                }

                if (self.startTime > self.endTime) {
                    // 鼠标移动
                    if (type.startsWith('mousemove')) {
                        // 将鼠标位置转换为标准化设备坐标（NDC）
                        self.mouse_2d_pos_move.x = (e.clientX / window.innerWidth) * 2 - 1;
                        self.mouse_2d_pos_move.y = -(e.clientY / window.innerHeight) * 2 + 1;
                    }
                    else if (type.startsWith('touchmove')) {
                        const touch = e.touches[0];
                        // 将触摸位置转换为标准化设备坐标（NDC）
                        self.mouse_2d_pos_move.x = (touch.clientX / window.innerWidth) * 2 - 1;
                        self.mouse_2d_pos_move.y = -(touch.clientY / window.innerHeight) * 2 + 1;
                    }

                    if (type.endsWith('move')) {
                        self.moveTime = Date.now();
                    }
                }

                // 鼠标按下
                if (type.endsWith('down') || type.endsWith('start')) {
                    self.startTime = Date.now();

                    // 指向对象就是选中物体就暂停视角控制
                    const objs = this.ctx.cgWorld.getSelectedObjects();
                    if (objs && objs.length > 0) {
                        // 当前指向的对象
                        const nowPointingAtCgo = this.ctx.cgWorld.getAbility('select-cgo-dev').nowPointingAtCgo;
                        for (const obj of objs) {
                            if (nowPointingAtCgo && nowPointingAtCgo.getId() === obj.getId()) {
                                // 记录三维位置
                                self.mouse_3d_pos_start = self._calTgtXZNewPos(self.ctx.camera, self.mouse_2d_pos_start, self.ctx.data.designer.focusGroundHeight);
                                self.obj_pos_start = obj.getPos();
                                // 暂停视角控制
                                const fpc = this.ctx.cgWorld.getAbility('first_person_controls');
                                fpc.deactive();
                                break;
                            }
                        }
                    }
                }
            });
    }

    /**
     * 更新（在动画循环中调用）
     */
    update() {
        const self = this;
        // 开始拖动对象
        if (self.startTime > self.endTime && Date.now() - self.moveTime < 50) {
            const objs = self.ctx.cgWorld.getSelectedObjects();
            if (objs && objs.length > 0) {
                self.is_in_select_mode = true;
                // 当前指向的对象
                const nowPointingAtCgo = self.ctx.cgWorld.getAbility('select-cgo-dev').nowPointingAtCgo;
                for (const obj of objs) {
                    if (nowPointingAtCgo && nowPointingAtCgo.getId() === obj.getId()) {
                        // 移动对象
                        const newPos = self._calTgtXZNewPos(self.ctx.camera, self.mouse_2d_pos_move, self.ctx.data.designer.focusGroundHeight);
                        if (newPos) {
                            const offset = { x: 0, y: 0, z: 0 };
                            offset.x = newPos[0] - self.mouse_3d_pos_start[0];
                            offset.z = newPos[2] - self.mouse_3d_pos_start[2];

                            const finalNewPos = [self.obj_pos_start[0] + offset.x, self.obj_pos_start[1] + offset.y, self.obj_pos_start[2] + offset.z];
                            const finalNewPos1 = self._getGridRealPosByRealPos(finalNewPos);
                            // console.log(finalNewPos1)

                            finalNewPos1.y = self.obj_pos_start[1];
                            obj.setPos(finalNewPos1);
                            self._touchUserMoveObject();
                            self._touchUserChangeObject();
                        }
                    }
                }
            }
        }
    }

    /**
     * 计算鼠标在xz面上的坐标，y是高度，已经指定好了
     * @param {*} camera 当前相机
     * @param {THREE.Vector2} mouse_2d_pos_move 鼠标移动坐标
     * @param {*} y 高度
     * @returns {[x, y, z]} 新的坐标
     */
    _calTgtXZNewPos(camera, mouse_2d_pos_move, y) {
        const { THREE } = this.ctx.imports;

        // 创建射线投射器
        const raycaster = new THREE.Raycaster();

        // 从相机位置向鼠标位置发射射线
        raycaster.setFromCamera(mouse_2d_pos_move, camera);

        // 创建一个在指定高度y的水平平面
        const plane = new THREE.Plane(new THREE.Vector3(0, 1, 0), -y);

        // 计算射线与平面的交点
        const intersectPoint = new THREE.Vector3();
        const intersected = raycaster.ray.intersectPlane(plane, intersectPoint);

        if (intersected) {
            return [intersectPoint.x, y, intersectPoint.z];
        }

        return null;
    }

    _touchUserChangeObject() {
        const self = this;
        clearTimeout(this._touchUserChangeObjectTimeout);
        this._touchUserChangeObjectTimeout = setTimeout(() => {
            self.ctx.cgWorld.getAbility('get_user_change_any_event').touchEvent('user-change-object', {});
        }, 500);
    }

    _touchUserMoveObject() {
        const self = this;
        clearTimeout(this._touchUserMoveObjectTimeout);
        this._touchUserMoveObjectTimeout = setTimeout(() => {
            self.ctx.cgWorld.getAbility('get_user_change_any_event').touchEvent('user-move-object', {});
        }, 100);
    }

    /**
     * 获取磁吸网格坐标
     */
    _getGridRealPosByRealPos(pos) {
        // console.log(this.getRealSize(ctx.data.designer.magneticGridSizeUnit), ctx.data.designer.groundGrid.totalGridCount, pos)
        return getXZGridRealPosByRealPos(this.getRealSize(ctx.data.designer.magneticGridSizeUnit),
            ctx.data.designer.groundGrid.totalGridCount, pos);
    }
}