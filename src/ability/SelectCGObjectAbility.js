/**
 * 选中游戏对象能力
 */
export class SelectCGObjectAbility {

    /**
     * 
     * @param {*} ctx 
     * @param {*} data singleSelectMode: false
     */
    constructor(ctx, data) {
        this.ctx = ctx;
        this.data = data;

        // 当前指向的对象
        this.nowPointingAtCgo;
    }

    /**
     * 初始化
     * @param {*} pars 
     */
    init(pars) {

    }

    /**
     * 启动
     */
    start(pars) {
        const self = this;
        const { cgWorld } = pars;
        const { THREE } = this.ctx.imports;
        const raycaster = new THREE.Raycaster();
        const userControlsAbility = cgWorld.getAbility('user_ctrl');

        // 鼠标按下时的时间戳
        let startTime = 0;
        // 鼠标按下时的坐标
        const mouse_2d_pos_start = new THREE.Vector2();

        /**
         * 点击事件
         * @param {*} e 
         * @param {*} type 
         */
        const onClickFunc = (e, type) => {
            // console.log('select-cgo', type, e);
            const objs = self._getFirstIntersectObject(raycaster, mouse_2d_pos_start, self.ctx.camera, self.ctx.scene);
            for (const obj of objs) {
                if (self.isSingleSelectMode()) {
                    // 单选模式，先清空所有选中
                    const selectedObjs = cgWorld.getSelectedObjects({
                        excludeIds: [obj.getId()]
                    });
                    for (const item of selectedObjs) {
                        item.deactiveSelectionMode();
                    }
                }

                // 取消选中模式
                if (obj.isSelectionModeActive()) {
                    obj.deactiveSelectionMode();
                    
                    const gucaeAbility = cgWorld.getAbility('get_user_change_any_event');
                    gucaeAbility.touchEvent('user-cancel-select-object', { target: obj });
                }
                // 激活选中模式
                else {
                    obj.activeSelectionMode();
                    
                    const gucaeAbility = cgWorld.getAbility('get_user_change_any_event');
                    gucaeAbility.touchEvent('user-select-object', { target: obj });
                }

                break;
            }
        };

        /**
         * 拦截鼠标或触摸事件
         * @param {MouseEvent|TouchEvent} e
         */
        userControlsAbility.setUserEvent('select-cgo', ["mouseleftdown", "mouseup", "touchstart", "touchend"], (e, type) => {
            // 鼠标按下
            if (type.startsWith('mouseleftdown')) {
                // 将鼠标位置转换为标准化设备坐标（NDC）
                mouse_2d_pos_start.x = (e.clientX / window.innerWidth) * 2 - 1;
                mouse_2d_pos_start.y = -(e.clientY / window.innerHeight) * 2 + 1;
            }
            else if (type.startsWith('touchstart')) {
                const touch = e.touches[0];
                // 将触摸位置转换为标准化设备坐标（NDC）
                mouse_2d_pos_start.x = (touch.clientX / window.innerWidth) * 2 - 1;
                mouse_2d_pos_start.y = -(touch.clientY / window.innerHeight) * 2 + 1;
            }

            // 鼠标按下
            if (type.endsWith('down') || type.endsWith('start')) {
                startTime = Date.now();

                const objs = self._getFirstIntersectObject(raycaster, mouse_2d_pos_start, self.ctx.camera, self.ctx.scene);
                if (objs && objs.length > 0) {
                    for (const obj of objs) {
                        self.nowPointingAtCgo = obj;
                        // console.log(`设置鼠标指向对象`, obj)
                        break;
                    }
                }
                else {
                    self.nowPointingAtCgo = null;
                }
            }
            // 鼠标抬起
            else {
                const t = Date.now() - startTime;
                if (t < 200) {
                    onClickFunc(e, type);
                }
            }
        });
    }

    /**
     * 获取第一个被射线射中物体
     * @param {THREE.Raycaster} raycaster 
     * @param {THREE.Vector2} mouse_2d_pos_start 
     * @param {*} camera 
     * @param {*} scene 
     */
    _getFirstIntersectObject(raycaster, mouse_2d_pos_start, camera, scene) {
        const { THREE } = this.ctx.imports;

        // 使用相机的射线投射
        raycaster.setFromCamera(mouse_2d_pos_start, camera);

        // 计算物体和射线的交点
        const intersects = raycaster.intersectObjects(scene.children);
        // console.log(intersects)

        const list = [];
        for (const item of intersects) {
            let selectedCgo = item.object.userData.cgo;
            if (selectedCgo == null && item.object.parent.isGroup) {
                selectedCgo = item.object.parent.userData.cgo;
            }
            if (selectedCgo == null && item.object.parent.userData.cgo) {
                selectedCgo = item.object.parent.userData.cgo;
            }

            if (item.object.isSky) {
            }
            else if (item.object.type === 'GridHelper') {
            }
            else if (item.object.type === 'BoxHelper') {
            }
            else if (item.object.type === 'AxesHelper') {
            }
            else if (item.object.type === 'CameraHelper') {
            }
            else if (item.object.userData != null && item.object.userData.canSelect === false) {
                // 标记不能选中
            }
            else if (selectedCgo != null && selectedCgo.data.canSelect === false) {
                // 标记不能选中
            }
            else if (selectedCgo != null) {
                list.push(selectedCgo);
            }
            else {
                console.log('没有cgo', item);
            }
        }
        return list;
    }

    /**
     * 是否为单选模式
     */
    isSingleSelectMode() {
        if (this.data.singleSelectMode == null) {
            this.data.singleSelectMode = false;
        }
        return this.data.singleSelectMode;
    }
}