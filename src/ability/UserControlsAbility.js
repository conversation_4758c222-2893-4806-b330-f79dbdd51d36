/**
 * 用户控制能力
 * 通过鼠标键盘触摸屏来控制
 */
export class UserControlsAbility {

    constructor(ctx, data) {
        this.ctx = ctx;
        this.data = data;

        this._userEventMap = {};
        this._mouseDownTime = null;
    }

    /**
     * 初始化
     * @param {*} pars 
     */
    init(pars) {
        const self = this;

        // 定义事件【pc端】
        self._mousedown = function (e) {
            if (self.debug) {
                console.log(`【调试】mousedown -> ${e}`);
                console.log(e);
            }
            for (let key in self._userEventMap) {
                const event = self._userEventMap[key];

                if (event.types.indexOf("mousedown") !== -1 &&
                    event.func instanceof Function) {
                    event.func(e, 'mousedown');
                }

                // 鼠标左键按下
                if (e.button === 0) {
                    if (event.types.indexOf("mouseleftdown") !== -1 &&
                        event.func instanceof Function) {
                        event.func(e, 'mouseleftdown');
                    }
                }
                // 鼠标中键按下
                else if (e.button === 1) {
                    if (event.types.indexOf("mousemiddledown") !== -1 &&
                        event.func instanceof Function) {
                        event.func(e, 'mousemiddledown');
                    }
                }
                // 鼠标右键按下
                else if (e.button === 2) {
                    if (event.types.indexOf("mouserightdown") !== -1 &&
                        event.func instanceof Function) {
                        event.func(e, 'mouserightdown');
                    }
                }

            }
        }

        self._mouseup = function (e) {
            if (self.debug) {
                console.log(`【调试】mouseup -> ${e}`);
                console.log(e);
            }
            for (let key in self._userEventMap) {
                const event = self._userEventMap[key];
                if (event.types.indexOf("mouseup") !== -1 &&
                    event.func instanceof Function) {
                    event.func(e, 'mouseup');
                }
            }
        }

        self._mousemove = function (e) {
            for (let key in self._userEventMap) {
                const event = self._userEventMap[key];
                if (event.types.indexOf("mousemove") !== -1 &&
                    event.func instanceof Function) {
                    event.func(e, 'mousemove');
                }
            }
        }

        self._keydown = function (e) {
            if (self.debug) {
                console.log(`【调试】keydown -> ${e}`);
                console.log(e);
            }
            for (let key in self._userEventMap) {
                const event = self._userEventMap[key];
                if (event.types.indexOf("keydown") !== -1 &&
                    event.func instanceof Function) {
                    event.func(e, 'keydown');
                }
            }
        }

        self._keyup = function (e) {
            if (self.debug) {
                console.log(`【调试】keyup -> ${e}`);
                console.log(e);
            }
            for (let key in self._userEventMap) {
                const event = self._userEventMap[key];
                if (event.types.indexOf("keyup") !== -1 &&
                    event.func instanceof Function) {
                    event.func(e, 'keyup');
                }
            }
        }

        // 定义事件【移动端】
        self._touchstart = function (e) {
            if (self.debug) {
                console.log(`【调试】touchstart -> ${e}`);
                console.log(e);
            }
            for (let key in self._userEventMap) {
                const event = self._userEventMap[key];
                if (event.types.indexOf("touchstart") !== -1 &&
                    event.func instanceof Function) {
                    event.func(e, 'touchstart');
                }
            }
        }

        self._touchmove = function (e) {
            if (self.debug) {
                console.log(`【调试】touchmove -> ${e}`);
                console.log(e);
            }
            for (let key in self._userEventMap) {
                const event = self._userEventMap[key];
                if (event.types.indexOf("touchmove") !== -1 &&
                    event.func instanceof Function) {
                    event.func(e, 'touchmove');
                }
            }
        }

        self._touchend = function (e) {
            if (self.debug) {
                console.log(`【调试】touchend -> ${e}`);
                console.log(e);
            }
            for (let key in self._userEventMap) {
                const event = self._userEventMap[key];
                if (event.types.indexOf("touchend") !== -1 &&
                    event.func instanceof Function) {
                    event.func(e, 'touchend');
                }
            }
        }
    }

    /**
     * 启动
     */
    start() {
        let el = document.getElementById('gameCanvas');
        // 拦截事件【pc端】
        el.addEventListener(
            "mousedown",
            this._mousedown,
            false
        );
        el.addEventListener(
            "mouseup",
            this._mouseup,
            false
        );
        el.addEventListener(
            "mousemove",
            this._mousemove,
            false
        );
        el.addEventListener(
            "keydown",
            this._keydown,
            false
        );
        el.addEventListener(
            "keyup",
            this._keyup,
            false
        );
        // 拦截事件【移动端】
        el.addEventListener(
            "touchstart",
            this._touchstart,
            false
        );
        el.addEventListener(
            "touchmove",
            this._touchmove,
            false
        );
        el.addEventListener(
            "touchend",
            this._touchend,
            false
        );
    }

    /**
     * 注册用户事件
     * @param key {string}
     * @param types {string[]} 例：["mousedown", "mouseup", "mousemove"]
     * @param func {function}
     */
    setUserEvent(key, types, func) {
        if (this.debug) {
            console.log(`【调试】注册用户事件 -> ${key}`);
        }

        if (this._userEventMap[key] !== undefined) {
            throw new Error(`重复注册用户事件 -> ${key}`);
        }
        else {
            this._userEventMap[key] = {
                types: types,
                func: func
            };
        }
    }

    /**
     * 删除用户事件
     * @param key
     */
    deleteUserEvent(key) {
        if (this.debug) {
            console.log(`【调试】删除用户事件 -> ${key}`);
        }
        delete this._userEventMap[key];
    }

    /**
     * 取消事件拦截
     */
    deactive() {
        // 取消事件拦截【pc端】
        document.removeEventListener(
            "mousedown",
            this._mousedown,
            false
        );
        document.removeEventListener(
            "mouseup",
            this._mouseup,
            false
        );
        document.removeEventListener(
            "mousemove",
            this._mousemove,
            false
        );
        document.removeEventListener(
            "keydown",
            this._keydown,
            false
        );
        document.removeEventListener(
            "keyup",
            this._keyup,
            false
        );
        // 取消事件拦截【移动端】
        document.removeEventListener(
            "touchstart",
            this._touchstart,
            false
        );
        document.removeEventListener(
            "touchmove",
            this._touchmove,
            false
        );
        document.removeEventListener(
            "touchend",
            this._touchend,
            false
        );
    }
}