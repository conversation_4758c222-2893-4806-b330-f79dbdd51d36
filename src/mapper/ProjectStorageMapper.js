import { reqP<PERSON><PERSON><PERSON> } from "../util/http-util.js";
/**
 * 项目存储
 */
export class ProjectStorageMapper {
    constructor(ctx) {
        this.ctx = ctx;
    }

    async save(id, name, data) {
        reqPost<PERSON>son('/api/game-editor/project/save', {
            id,
            name,
            data,
        })
    }

    async load(id) {
        const res = await req<PERSON><PERSON><PERSON><PERSON>('/api/game-editor/project/load', {
            id,
        })
        return res.data;
    }
}