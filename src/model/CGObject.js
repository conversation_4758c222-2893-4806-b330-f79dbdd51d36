import { newCUIdA } from "../util/id-util.js";
export class CGObject {

    constructor(ctx, data = {}) {
        this.ctx = ctx;
        this.data = data;

        // ID
        if (this.data.id == null) this.data.id = newCUIdA();
    }

    getId() {
        return this.data.id;
    }

    getPropValue(name) {
        return this.data[name];
    }

    async setPropValue(name, value) {
        const list = this.constructor.getProps();
        const prop = list.find(item => item.name == name);
        if (prop) {
            this.data[name] = value;
            return true;
        }
    }

    static getProps() {
        return [
            { label: 'ID', name: 'id', type: 'String', readonly: true },
        ]
    }

    getRealSize(sizeUnit) {
        return this.ctx.utils.getRealSize(sizeUnit);
    }

    getRealSizeList(sizeUnitList) {
        return this.ctx.utils.getRealSizeList(sizeUnitList);
    }

    getUnitSize(realSize) {
        return this.ctx.utils.getUnitSize(realSize);
    }

    getUnitSizeList(realSizeList) {
        return this.ctx.utils.getUnitSizeList(realSizeList);
    }


}