import { tjs_createCube } from "../../util/tjs-geometry-util";

export class ModelCreatorPart {
    constructor(ctx, data) {
        this.ctx = ctx;
        this.data = data;
    }

    build(pars) {
        const { THREE } = this.ctx.imports;

        let type = pars.type;
        let sizeX = pars.sizeX;
        let sizeY = pars.sizeY;
        let sizeZ = pars.sizeZ;

        // 立方体
        if (type === 'cube') {
            const cubeMesh = tjs_createCube(this.ctx, sizeX, sizeY, sizeZ, {
                imageSource: pars.parent.data.imageSource
            });

            cubeMesh.castShadow = true;
            cubeMesh.receiveShadow = true;
            this.ctx.scene.add(cubeMesh);

            // 对齐点设为底部
            if (pars.parent.data.alignmentPointType === 'bottom') {
                cubeMesh.geometry.translate(0, (sizeY / 2), 0);
            }

            return cubeMesh;
        }
        // 地面
        else if (type === 'ground') {
            const groundGeometry = new THREE.PlaneGeometry(sizeX, sizeZ);
            const groundMaterial = new THREE.MeshStandardMaterial({
                color: 0x808080,
                // side: THREE.DoubleSidde, // 两面都显示
            });
            const ground = new THREE.Mesh(groundGeometry, groundMaterial);
            ground.position.y = 0;
            ground.receiveShadow = true;
            this.ctx.scene.add(ground);

            // *** 围绕边缘的墙 ***
            if (pars.aroundBorderWalls) {
                let wallHeightUnit = 2;
                let farBorderUnit = 0;
                const wallHeight = this.ctx.utils.getRealSize(wallHeightUnit);
                // 远离边缘的距离
                let farBorder = this.ctx.utils.getRealSize(farBorderUnit);
                for (let i = 0; i < 4; i++) {
                    const aroundWallGeometry = new THREE.PlaneGeometry(sizeX, wallHeight);
                    const aroundWallMaterial = new THREE.MeshStandardMaterial({
                        color: 0x808080,
                        // side: THREE.DoubleSide, // 两面都显示
                        transparent: true,
                        opacity: 0.05,
                    });
                    const aroundWall = new THREE.Mesh(aroundWallGeometry, aroundWallMaterial);
                    aroundWall.receiveShadow = true;
                    // aroundWall.position.y = wallHeight / 2;

                    if (i === 0) {
                        aroundWall.position.z = wallHeight / 2;

                        aroundWall.rotation.x = -Math.PI / 2;

                        aroundWall.position.y = -sizeX / 2 + farBorder;
                    }
                    else if (i === 1) {
                        aroundWall.position.z = wallHeight / 2;

                        aroundWall.rotation.y = -Math.PI / 2;
                        aroundWall.rotation.x = -Math.PI / 2;

                        aroundWall.position.x = sizeX / 2 - farBorder;
                    }
                    else if (i === 2) {
                        aroundWall.position.z = wallHeight / 2;

                        aroundWall.rotation.x = Math.PI / 2;

                        aroundWall.position.y = sizeX / 2 + farBorder;
                    }
                    else if (i === 3) {
                        aroundWall.position.z = wallHeight / 2;

                        aroundWall.rotation.y = Math.PI / 2;
                        aroundWall.rotation.x = Math.PI / 2;

                        aroundWall.position.x = -sizeX / 2 + farBorder;
                    }

                    ground.add(aroundWall);
                }
            }

            ground.rotation.x = -Math.PI / 2;

            return ground;
        }
        else if (type === 'wall') {

        }
    }
}