export class HelperCreatorPart {
    constructor(ctx, data = {}) {
        this.ctx = ctx;
        this.data = data;
    }

    build(pars) {
        const { CSS2DObject, THREE } = this.ctx.imports;
        const { type } = pars;

        // 中心坐标指示器
        if (type === 'center-axes-indicator') {
            this.cfg = {};
            // 中心点坐标指示器尺寸
            if (this.cfg.caiAxesSize == null) this.cfg.caiAxesSize = 200;
            if (this.cfg.caiArrowRadius == null) this.cfg.caiArrowRadius = 1;

            let arrowLabelDistance = 5;

            const axesHelper = new THREE.AxesHelper(this.cfg.caiAxesSize);
            axesHelper.renderOrder = 1;
            axesHelper.position.set(0, 0, 0);

            {
                // x轴箭头（红色）
                const geometry = new THREE.ConeGeometry(this.cfg.caiArrowRadius, this.cfg.caiAxesSize / 5, 32);
                const material = new THREE.MeshBasicMaterial({ color: 'red' });
                const cone = new THREE.Mesh(geometry, material);
                cone.position.set(this.cfg.caiAxesSize, 0, 0);
                cone.rotateZ(-Math.PI / 180 * 90);
                axesHelper.add(cone);

                // x轴标签
                const labelDiv = document.createElement('div');
                labelDiv.textContent = '+X';
                labelDiv.style = 'color:silver;';
                const css2dLabel = new CSS2DObject(labelDiv);
                css2dLabel.position.set(this.cfg.caiAxesSize + arrowLabelDistance, 0, 0);
                axesHelper.add(css2dLabel);
            }
            {
                // y轴箭头（绿色）
                const geometry = new THREE.ConeGeometry(this.cfg.caiArrowRadius, this.cfg.caiAxesSize / 5, 32);
                const material = new THREE.MeshBasicMaterial({ color: 'green' });
                const cone = new THREE.Mesh(geometry, material);
                cone.position.set(0, this.cfg.caiAxesSize, 0);
                axesHelper.add(cone);

                // y轴标签
                const labelDiv = document.createElement('div');
                labelDiv.textContent = '+Y';
                labelDiv.style = 'color:silver;';
                const css2dLabel = new CSS2DObject(labelDiv);
                css2dLabel.position.set(0, this.cfg.caiAxesSize + arrowLabelDistance, 0);
                axesHelper.add(css2dLabel);
            }
            {
                // z轴箭头（蓝色）
                const geometry = new THREE.ConeGeometry(this.cfg.caiArrowRadius, this.cfg.caiAxesSize / 5, 32);
                const material = new THREE.MeshBasicMaterial({ color: 'blue' });
                const cone = new THREE.Mesh(geometry, material);
                cone.position.set(0, 0, this.cfg.caiAxesSize);
                cone.rotateX(Math.PI / 180 * 90);
                axesHelper.add(cone);

                // z轴标签
                const labelDiv = document.createElement('div');
                labelDiv.textContent = '+Z';
                labelDiv.style = 'color:silver;';
                const css2dLabel = new CSS2DObject(labelDiv);
                css2dLabel.position.set(0, 0, this.cfg.caiAxesSize + arrowLabelDistance);
                axesHelper.add(css2dLabel);
            }

            this.ctx.scene.add(axesHelper);
        }
    }
}