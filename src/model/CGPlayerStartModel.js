import { tjs_createCylinder, tjs_createCylinderByTwoPoint, tjs_createSphere } from "../util/tjs-geometry-util";
import { CGObjectModel } from "./CGObjectModel";
/**
 * 玩家起点
 * 用于给设计师定位玩家开始游戏时的出生点
 */
export class CGPlayerStartModel extends CGObjectModel {
    constructor(ctx, data) {
        super(ctx, data);
    }

    async build() {

        if (this.model == null) {
            const opacity = 0.8;
            let headRadius = 0.3;
            let heightUnit = this.ctx.data.player.heightUnit;
            heightUnit = 1;

            // *** 创建身体 ***
            let sizeY = this.getRealSize(heightUnit);
            this.model = tjs_createCylinder(this.ctx, this.getRealSize(0.2), this.getRealSize(0.5), sizeY, {
                opacity
            });

            // 对齐点设为底部
            this.model.geometry.translate(0, (sizeY / 2), 0);
            this.ctx.scene.add(this.model);
            // this.model.rotation.x = Math.PI * 0.5;

            // *** 创建头部 ***
            const sphere = tjs_createSphere(this.ctx, this.getRealSize(headRadius), {
                opacity
            });
            sphere.position.y = this.getRealSize(heightUnit + headRadius + 0.1);
            this.model.add(sphere);

            // *** 创建朝向指示器 ***
            const directionIndicator = this.createDirectionIndicator({
                headRadius, heightUnit
            });
            this.model.add(directionIndicator);
        }

        await super.build();
    }

    /**
     * 创建朝向指示器
     */
    createDirectionIndicator(pars) {

        let fromPos = [0, this.getRealSize(pars.headRadius + pars.heightUnit + 0.1), 0];
        let toPos = [0, 0, 0];

        if (this.data.lookAtPosUnitX != null) {
            toPos[0] = this.getRealSize(this.data.lookAtPosUnitX);
        }
        else {
            toPos[0] = this.getRealSize(this.ctx.data.player.startLookAtPosUnit[0]);
        }

        if (this.data.lookAtPosUnitY != null) {
            toPos[1] = this.getRealSize(pars.headRadius + pars.heightUnit + 0.1) + this.getRealSize(this.data.lookAtPosUnitY);
        }
        else {
            toPos[1] = this.getRealSize(pars.headRadius + pars.heightUnit + 0.1) + this.getRealSize(this.ctx.data.player.startLookAtPosUnit[1]);
        }

        if (this.data.lookAtPosUnitZ != null) {
            toPos[2] = this.getRealSize(this.data.lookAtPosUnitZ);
        }
        else {
            toPos[2] = this.getRealSize(this.ctx.data.player.startLookAtPosUnit[2]);
        }

        const indicator = tjs_createCylinderByTwoPoint(this.ctx, 1, 3, toPos, fromPos);
        return indicator;
    }

    onFirstBuildEnd() {
        this.data.lookAtPosUnitX = this.ctx.data.player.startLookAtPosUnit[0];
        this.data.lookAtPosUnitY = this.ctx.data.player.startLookAtPosUnit[1];
        this.data.lookAtPosUnitZ = this.ctx.data.player.startLookAtPosUnit[2];

        this.setPosUnit(this.ctx.data.player.startPosUnit, { touchEvent: false });
    }

    onPosChange() {
        this.ctx.data.player.startPosUnit = this.getPosUnit();
    }

    async setPropValue(name, value) {
        const list = this.constructor.getProps({ onlySelf: true });
        const prop = list.find(item => item.name == name);
        if (prop) {
            this.data[name] = value;

            if (name === 'lookAtPosUnitX') {
                this.ctx.data.player.startLookAtPosUnit[0] = value;
            }
            else if (name === 'lookAtPosUnitY') {
                this.ctx.data.player.startLookAtPosUnit[1] = value;
            }
            else if (name === 'lookAtPosUnitZ') {
                this.ctx.data.player.startLookAtPosUnit[2] = value;
            }

            this.destroy();
            await this.build();
            return true;
        }
        else {
            return super.setPropValue(name, value);
        }
    }

    static getProps(opts = {}) {
        const list = [];
        list.push(...[
            { label: '朝向点X', name: 'lookAtPosUnitX', type: 'Number', default: 0, step: 1 },
            { label: '朝向点Y', name: 'lookAtPosUnitY', type: 'Number', default: 0, step: 1 },
            { label: '朝向点Z', name: 'lookAtPosUnitZ', type: 'Number', default: 0, step: 1 },
        ]);
        // if (opts.onlySelf !== true) {
        //     list.push(...super.getProps());
        // }
        return list;
    }
}
