import { importFBXModel } from "../util/import-fbx-model-util.js";
import { ModelCreatorPart } from "./part/ModelCreatorPart.js";
import { PhysBodyCreatorPart } from "./part/PhysBodyCreatorPart.js";
import { HelperCreatorPart } from './part/HelperCreatorPart.js';
import { CGObject } from "./CGObject.js";
import { tjs_createAxesIndicator, tjs_destroyAxesIndicator } from "../util/tjs-axes-util.js";

/**
 * CS Game Object Model
 * 游戏对象模型
 */
export class CGObjectModel extends CGObject {
    constructor(ctx, data = {}) {
        super(ctx, data);

        // *** 数据 ***
        // pos: [0, 0, 0] 初始坐标
        // modelCreator: null 模型创建器
        // modelLoader: null 外部模型加载器
        // physBody: null 物理体
        // 坐标
        if (this.data.pos == null) this.data.pos = [0, 0, 0];
        // 缩放比例
        if (this.data.scale == null) this.data.scale = 1;
        // 是否被选中
        if (this.data.isSelected == null) this.data.isSelected = false;

        // *** 系统参数 ***
        // 可见模型
        this.model = null;
        // 物理体
        this.physBody = null;
        // 模型边界盒
        this.modelBox = null;

        this.moreModels = [];
        // 联动对象
        this.togetherItemsMap = {};
        // 激活选中模式的时间戳
        this.activeSelectionModeTime = 0;

        this.buildCount = 0;

        this.sizeX = 10;
        this.sizeY = 10;
        this.sizeZ = 10;
    }

    async build() {
        const { THREE } = this.ctx.imports;

        if (this.data.sizeXUnit >= 0) {
            this.sizeX = this.data.sizeXUnit * this.ctx.data.world.unitSize;
        }
        if (this.data.sizeYUnit >= 0) {
            this.sizeY = this.data.sizeYUnit * this.ctx.data.world.unitSize;
        }
        if (this.data.sizeZUnit >= 0) {
            this.sizeZ = this.data.sizeZUnit * this.ctx.data.world.unitSize;
        }

        // 创建模型
        if (this.data.modelCreator) {
            if (this.model == null) {
                if (this.modelCreatorPart == null) {
                    this.modelCreatorPart = new ModelCreatorPart(this.ctx, {
                        parent: self,
                    });
                }
                this.model = this.modelCreatorPart.build({
                    parent: this,
                    ...this.data.modelCreator,
                    sizeX: this.sizeX,
                    sizeY: this.sizeY,
                    sizeZ: this.sizeZ,
                });
            }
        }
        // 加载外部模型
        if (this.data.modelLoader) {
            if (this.model == null) {
                const loader = this.data.modelLoader;
                const importResult =
                    await importFBXModel(this.ctx, loader.url, {
                        debug: loader.debug || this.data.debug || this.ctx.debug,
                        baseFileName: this.data.baseFileName,
                    });
                const model = importResult.model;
                this.model = model;
                this.ctx.scene.add(model);
            }

            // 计算模型边界盒
            const box = this.getModelBox();
            const size = box.getSize(new THREE.Vector3());

            // // 计算缩放
            // if (this.sizeY > 0) {
            //     const scale = this.sizeY / size.y;
            //     this.data.scale = scale;
            //     this.model.scale.setScalar(scale);
            // }
        }
        // 可碰撞对象
        if (this.data.collidableObject) {
        }
        // 构建物理体
        if (this.data.physBody) {
            if (this.physBody == null) {
                if (this.physBodyCreatorPart == null) {
                    this.physBodyCreatorPart = new PhysBodyCreatorPart(this.ctx, {});
                }
                this.physBody = this.physBodyCreatorPart.build({
                    debug: this.debug(),
                    scale: this.data.scale,
                    box: this.getModelBox(),
                    ...this.data.physBody
                });
                this.togetherItemsMap['physBody'] = { core: this.physBody, pos: [0, 0, 0] };
            }
        }
        // 助手
        if (this.data.helper) {
            if (this.helperCreatorPart == null) {
                this.helperCreatorPart = new HelperCreatorPart(this.ctx, {});
            }
            this.helperCreatorPart.build({
                ...this.data.helper
            })
        }

        if (this.model) {
            if (this.model.userData == null) this.model.userData = {};
            this.model.userData.cgo = this;
        }
        if (this.moreModels) {
            for (const moreModel of this.moreModels) {
                if (moreModel.userData == null) moreModel.userData = {};
                moreModel.userData.cgo = this;
            }
        }

        // 恢复选中模式
        if (this.ctx.enableDevViewport) {
            if (this.data.isSelected) {
                this.activeSelectionMode();
                // if (this.getModel()) {
                //     const boxHelper = this.getModelBoxHelper();
                //     boxHelper.update();
                // }
            }
        }

        // 旋转
        if (this.data.rotationX != null) {
            this.model.rotation.x = this.data.rotationX * Math.PI / 180;
        }
        if (this.data.rotationY != null) {
            this.model.rotation.y = this.data.rotationY * Math.PI / 180;
        }
        if (this.data.rotationZ != null) {
            this.model.rotation.z = this.data.rotationZ * Math.PI / 180;
        }

        // 缩放
        if (this.data.scale != null && this.model) {
            this.model.scale.setScalar(this.data.scale);
        }

        // 定位
        if (this.data.posUnit) {
            this.setPosUnit(this.data.posUnit[0], this.data.posUnit[1], this.data.posUnit[2]);
        }
        else if (this.data.pos) {
            this.setPos(this.data.pos);
        }
        else {
            this.setPos(0, 0, 0);
        }

        if (this.buildCount === 0) {
            this.onFirstBuildEnd();
        }
        this.buildCount++;
    }

    update() {
        this.updateModelBoxHelper();
    }

    debug() {
        return this.debug || this.ctx.debug;
    }

    getPosUnit() {
        return this.ctx.utils.getUnitSizeList(this.data.pos);
    }

    setPosUnit(x, y, z) {
        if (x instanceof Array) {
            this.setPos(this.getRealSizeList(x));
        }
        if (x instanceof Object) {
            const t = x;
            x = t[0];
            y = t[1];
            z = t[2];
            this.setPos(this.ctx.utils.getRealSize(x), this.ctx.utils.getRealSize(y), this.ctx.utils.getRealSize(z));
        }
        else {
            this.setPos(this.ctx.utils.getRealSize(x), this.ctx.utils.getRealSize(y), this.ctx.utils.getRealSize(z));
        }
    }

    getPos() {
        return this.data.pos;
    }

    setPos(x, y, z, opts = {}) {

        if (x instanceof Array) {
            const t = x;
            opts = y ?? {};

            x = t[0];
            y = t[1];
            z = t[2];
        }
        else if (x instanceof Object) {
            const t = x;
            opts = y ?? {};

            x = t.x;
            y = t.y;
            z = t.z;
        }

        this.data.pos = [x, y, z];
        this.data.posUnit = [this.ctx.utils.getUnitSize(x), this.ctx.utils.getUnitSize(y), this.ctx.utils.getUnitSize(z)];

        if (this.model) this.model.position.set(x, y, z);

        for (const prop in this.togetherItemsMap) {
            const togetherItem = this.togetherItemsMap[prop];
            togetherItem.core.position.set(x + togetherItem.pos[0], y + togetherItem.pos[1], z + togetherItem.pos[2]);
        }

        this.update();

        if (opts.touchEvent !== false) {
            this.onPosChange();
        }
    }

    getData() {
        return this.data;
    }

    getTags() {
        return this.data.tags;
    }

    getModel() {
        return this.model;
    }

    getMoreModels() {
        return this.moreModels;
    }

    getModelBox() {
        const { THREE } = this.ctx.imports;
        if (this.modelBox == null) {
            this.modelBox = new THREE.Box3().setFromObject(this.getModel());
        }
        return this.modelBox;
    }

    getModelBoxHelper() {
        const { THREE } = this.ctx.imports;
        const model = this.getModel();
        if (model && this.modelBoxHelper == null) {
            this.modelBoxHelper = new THREE.BoxHelper(model, 0x00ff00);
            this.ctx.scene.add(this.modelBoxHelper);
        }
        return this.modelBoxHelper;
    }

    updateModelBoxHelper() {
        if (this.modelBoxHelper) {
            this.modelBoxHelper.update();
        }
    }

    destroyModelBoxHelper() {
        if (this.modelBoxHelper) {
            this.ctx.scene.remove(this.modelBoxHelper);
            this.modelBoxHelper = null;
        }
    }

    getPhysBody() {
        return this.physBody;
    }

    isSelectionModeActive() {
        return this.data.isSelected;
    }

    /**
     * 激活选中模式
     */
    activeSelectionMode() {
        if (this.getModel()) {
            const boxHelper = this.getModelBoxHelper();
            this.data.isSelected = true;
            this.activeSelectionModeTime = Date.now();

            // const axesHelper = tjs_createAxesIndicator(this.ctx);
            // this.model.add(axesHelper);

            console.log('激活选中模式：', this);
        }
    }

    /**
     * 取消选中模式
     */
    deactiveSelectionMode() {
        this.data.isSelected = false;
        this.destroyModelBoxHelper();

        // tjs_destroyAxesIndicator(this.model.getObjectByName('axesHelper'));
    }

    destroy() {
        // 删除可见模型
        this._destroyModel(this.model);
        this.model = null;

        // 删除更多模型
        if (this.moreModels) {
            for (const moreModel of this.moreModels) {
                this._destroyModel(moreModel);
            }
            this.moreModels = [];
        }

        // 删除物理体
        if (this.physBody) {
            this.ctx.physWorld.removeBody(this.physBody);
            this.physBody = null;
        }

        // 清空模型边界盒
        if (this.modelBox) {
            this.modelBox = null;
        }
        this.destroyModelBoxHelper();

        // 清空联动对象
        for (const prop in this.togetherItemsMap) {
            delete this.togetherItemsMap[prop];
        }
    }

    _destroyModel(model) {
        // 删除可见模型
        if (model.geometry) {
            model.geometry.dispose();
        }
        if (model.material) {
            if (Array.isArray(model.material)) {
                model.material.forEach(m => {
                    if (m.map) m.map.dispose();
                    m.dispose();
                });
            } else {
                if (model.material.map) model.material.map.dispose();
                model.material.dispose();
            }
        }
        this.ctx.scene.remove(model);
    }

    getPropValue(name) {
        if (name === 'posXUnit') {
            return this.data.posUnit[0];
        }
        else if (name === 'posYUnit') {
            return this.data.posUnit[1];
        }
        else if (name === 'posZUnit') {
            return this.data.posUnit[2];
        }
        return this.data[name];
    }

    async setPropValue(name, value) {
        const list = this.constructor.getProps();
        const prop = list.find(item => item.name == name);
        if (prop) {
            if (name === 'posXUnit') {
                this.data.posUnit[0] = value;
            }
            else if (name === 'posYUnit') {
                this.data.posUnit[1] = value;
            }
            else if (name === 'posZUnit') {
                this.data.posUnit[2] = value;
            }
            else {
                this.data[name] = value;
            }

            await this.build();
            return true;
        }
        else {
            return await super.setPropValue(name, value);
        }
    }

    static getProps(opts = {}) {
        const list = [];
        list.push(...[
            { label: '旋转X（度）', name: 'rotationX', type: 'Number', default: 0, step: 15 },
            { label: '旋转Y（度）', name: 'rotationY', type: 'Number', default: 0, step: 15 },
            { label: '旋转Z（度）', name: 'rotationZ', type: 'Number', default: 0, step: 15 },
            { label: '缩放（单位）', name: 'scale', type: 'Number', default: 1, step: 0.5 },
            { label: '坐标X（米）', name: 'posXUnit', type: 'Number', default: 0, step: 0.1 },
            { label: '坐标Y（米）', name: 'posYUnit', type: 'Number', default: 0, step: 0.1 },
            { label: '坐标Z（米）', name: 'posZUnit', type: 'Number', default: 0, step: 0.1 },
        ]);
        if (opts.onlySelf !== true) {
            list.push(...super.getProps());
        }
        return list;
    }

    onFirstBuildEnd() {

    }

    onPosChange() {

    }
}