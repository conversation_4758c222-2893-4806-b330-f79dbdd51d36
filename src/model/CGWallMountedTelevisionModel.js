import { tjs_createPlane } from "../util/tjs-geometry-util";
import { CGObjectModel } from "./CGObjectModel";
/**
 * 壁挂电视模型
 */
export class CGWallMountedTelevisionModel extends CGObjectModel {
    constructor(ctx, data) {
        super(ctx, data);

        if (data.videoSource == null) data.videoSource = '';
        // 长度
        if (data.sizeXUnit == null) data.sizeXUnit = 10;
        // 高度
        if (data.sizeYUnit == null) data.sizeYUnit = 6;
        // 厚度
        if (data.sizeZUnit == null) data.sizeZUnit = 0.1;

        this._hasCreateAudio = false;
    }

    async build() {
        if (this.model == null) {
            let url = this.getVideoUrl();

            // if (this.data.videoSource) {
            //     if (this.data.videoSource.startsWith('http')) {
            //         url = this.data.videoSource;
            //     }
            //     else {
            //         url = `./data/resource/${this.ctx.data.project.id}/video/${this.data.videoSource}`;
            //     }
            // }


            this.model = tjs_createPlane(this.ctx, this.getRealSize(this.data.sizeXUnit), this.getRealSize(this.data.sizeYUnit), {
                videoSource: url,
                alignmentPointType: 'bottom',
                side: 'front',
            });


            const back = tjs_createPlane(this.ctx, this.getRealSize(this.data.sizeXUnit), this.getRealSize(this.data.sizeYUnit), {
                side: 'back',
                alignmentPointType: 'bottom',
            });
            this.model.add(back);

            this.ctx.scene.add(this.model);
        }
        await super.build();
    }

    getVideoUrl() {
        if (this.data.videoSource) {
            if (this.data.videoSource.startsWith('http')) {
                return this.data.videoSource;
            }
            else {
                return `./data/resource/${this.ctx.data.project.id}/video/${this.data.videoSource}`;
            }
        }
    }

    destroy() {
        if (this.model) {
            if (self.model.userData.sound) {
                self.model.userData.sound.disconnect();
            }
        }

        super.destroy();
    }

    async setPropValue(name, value) {
        const list = this.constructor.getProps({ onlySelf: true });
        const prop = list.find(item => item.name == name);
        if (prop) {
            this.data[name] = value;
            this.destroy();
            await this.build();
            return true;
        }
        else {
            return await super.setPropValue(name, value);
        }
    }

    static getProps(opts = {}) {
        const list = [];
        list.push(...[
            { label: '视频源', name: 'videoSource', type: 'String' },
            { label: '长度（米）', name: 'sizeXUnit', type: 'Number' },
            { label: '高度（米）', name: 'sizeYUnit', type: 'Number' },
            { label: '厚度（米）', name: 'sizeZUnit', type: 'Number' },
        ]);
        if (opts.onlySelf !== true) {
            list.push(...super.getProps());
        }
        return list;
    }

    onFirstBuildEnd() {
        const self = this;
        const userControlsAbility = this.ctx.cgWorld.getAbility('user_ctrl');
        userControlsAbility.setUserEvent(`wall_mounted_television_${self.getId()}`,
            ["mouseleftdown", "touchstart"], (e, type) => {
                const video = self.model.userData.video;
                let sound = self.model.userData.sound;

                if (video) {
                    video.play();

                    if (!self._hasCreateAudio) {
                        self._hasCreateAudio = true;
                        const { THREE } = self.ctx.imports;

                        // 音频监听器
                        const listener = new THREE.AudioListener();
                        self.ctx.camera.add(listener);

                        // 声音源（方法1）
                        // sound = new THREE.PositionalAudio(listener);
                        // // 加载视频音轨作为音频源
                        // // const mediaElementSource = listener.context.createMediaElementSource(video);
                        // // sound.setNodeSource(mediaElementSource);
                        // sound.setMediaElementSource(video);
                        // sound.setRefDistance(1);   // 声音在 1 米时音量最大
                        // sound.setRolloffFactor(1); // 衰减速度（可以调大调小）
                        // sound.setDistanceModel('linear'); // 线性衰减
                        // // sound.setVolume(1); // 设置初始音量
                        // self.model.add(sound); // model是Mesh

                        // 声音源（方法2-可行）
                        sound = new THREE.PositionalAudio(listener);
                        self.model.add(sound); // model是Mesh

                        let audioLoader = new THREE.AudioLoader();
                        // 加载音频文件，返回一个音频缓冲区对象作为回调函数参数
                        audioLoader.load(video.src, function (AudioBuffer) {
                            // console.log(buffer);
                            // 音频缓冲区对象关联到音频对象audio
                            sound.setBuffer(AudioBuffer);
                            sound.setVolume(3); //音量
                            sound.setLoop(true);
                            sound.setRefDistance(50); //参数值越大,声音越大
                            sound.setRolloffFactor(1); // 衰减速度（可以调大调小）
                            sound.play(); //播放
                        });
                    }
                }
            });
    }
}