import { tjs_createAxesIndicator } from "../util/tjs-axes-util";
import { tjs_calCameraLookAtPoint } from "../util/tjs-camera-util";
import { CGObjectModel } from "./CGObjectModel";
/**
 * 顶部坐标指示器（帮助用户知道当前方向坐标朝向）
 */
export class CGHUDAxesIndicator extends CGObjectModel {
    constructor(ctx, data) {
        super(ctx, data);

        this.smallWidth = 200;
        this.smallHeight = 200;
    }

    async build() {
        if (this.model == null) {
            let oCamSize = 15;

            const { THREE, CSS2DRenderer } = this.ctx.imports;

            // 创建坐标指示器（THREE.AxesHelper）
            this.model = tjs_createAxesIndicator(this.ctx, {
                caiAxesSize: this.smallHeight * 0.05,
                arrowLabelDistance: 1,
                caiArrowRadius: 0.3,
            });

            this.hudRenderer = new THREE.WebGLRenderer({ antialias: true, logarithmicDepthBuffer: true });
            this.hudRenderer.setSize(this.smallWidth, this.smallHeight);
            this.hudRenderer.setClearColor(0x000000, 0); // required
            this.hudRenderer.domElement.style.position = 'absolute';
            this.hudRenderer.domElement.style.top = '0px';
            this.hudRenderer.domElement.style.left = `${(window.innerWidth / 2) - (this.smallWidth / 2)}px`;

            this.hudRenderer.domElement.style.zIndex = '999';
            document.body.appendChild(this.hudRenderer.domElement);

            this.hudCss2dRenderer = new CSS2DRenderer();
            this.hudCss2dRenderer.setSize(this.smallWidth, this.smallHeight);
            this.hudCss2dRenderer.domElement.style.position = 'absolute';
            this.hudCss2dRenderer.domElement.style.top = '0px';
            this.hudCss2dRenderer.domElement.style.left = `${(window.innerWidth / 2) - (this.smallWidth / 2)}px`;
            this.hudCss2dRenderer.domElement.style.zIndex = '999';
            document.body.appendChild(this.hudCss2dRenderer.domElement);



            this.hudScene = new THREE.Scene();
            this.hudCamera = new THREE.OrthographicCamera(this.smallWidth / -oCamSize, this.smallWidth / oCamSize,
                this.smallHeight / oCamSize, this.smallHeight / -oCamSize, 0.01, 1000);
            this.hudCamera.position.set(0, 0, 0);
            this.hudScene.add(this.model);
        }
        await super.build();

        // console.log('build', this.model)
        this.resize();
    }

    resize() {
        this.hudRenderer.domElement.style.left = `${(window.innerWidth / 2) - (this.smallWidth / 2)}px`;
        this.hudCss2dRenderer.domElement.style.left = `${(window.innerWidth / 2) - (this.smallWidth / 2)}px`;
    }

    // 在animate中调用
    update() {
        // console.log('update', this.model)
        if (this.model) {
            this.hudCamera.rotation.copy(this.ctx.camera.rotation);

            // 与中心点保持一定距离并看向中心点
            const p0 = tjs_calCameraLookAtPoint(this.ctx, this.hudCamera, 100);
            this.hudCamera.position.sub(p0);
            this.hudCamera.lookAt(this.model.position);


            this.hudRenderer.render(this.hudScene, this.hudCamera);
            this.hudCss2dRenderer.render(this.hudScene, this.hudCamera);
        }
    }
}