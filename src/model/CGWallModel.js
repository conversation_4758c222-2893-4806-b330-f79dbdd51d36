import { CGObjectModel } from "./CGObjectModel";
/**
 * 墙体模型
 */
export class CGWallModel extends CGObjectModel {
    constructor(ctx, data) {
        super(ctx, data);

        // 长度
        if (data.sizeXUnit == null) data.sizeXUnit = 10;
        // 高度
        if (data.sizeYUnit == null) data.sizeYUnit = 6;
        // 厚度
        if (data.sizeZUnit == null) data.sizeZUnit = 0.2;
        // 对齐点设为底部
        data.alignmentPointType = 'bottom';

        data.modelCreator = {
            type: 'cube',
        };

        data.collidableObject = {};
    }

    async build() {
        await super.build();
    }

    async setPropValue(name, value) {
        const list = this.constructor.getProps({ onlySelf: true });
        const prop = list.find(item => item.name == name);
        if (prop) {
            this.data[name] = value;
            this.destroy();
            await this.build();
            return true;
        }
        else {
            return await super.setPropValue(name, value);
        }
    }

    static getProps(opts = {}) {
        const list = [];
        list.push(...[
            { label: '长度（米）', name: 'sizeXUnit', type: 'Number' },
            { label: '高度（米）', name: 'sizeYUnit', type: 'Number' },
            { label: '厚度（米）', name: 'sizeZUnit', type: 'Number' },
        ]);
        if (opts.onlySelf !== true) {
            list.push(...super.getProps());
        }
        return list;
    }
}