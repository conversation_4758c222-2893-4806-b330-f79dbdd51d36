import { CGObjectModel } from "./CGObjectModel.js";
/**
 * 天空模型
 */
export class CGSkyModel extends CGObjectModel {
    constructor(ctx, data) {
        super(ctx, data);

        const baseData = {
            turbidity: 5,          // 大气浑浊度，控制天空的雾霾程度
            rayleigh: 1,            // 瑞利散射系数，影响天空的蓝色程度
            mieCoefficient: 0.05,  // 米氏散射系数，影响大气中颗粒物的散射
            mieDirectionalG: 0.99999,   // 米氏散射方向性参数，控制散射的方向性
            elevation: 50,           // 太阳仰角（度），控制太阳在天空中的高度
            azimuth: 180,           // 太阳方位角（度），控制太阳的方向
            exposure: ctx.renderer.toneMappingExposure,  // 曝光度，控制整体亮度
        };

        for (const prop in baseData) {
            if (data[prop] == null) {
                data[prop] = baseData[prop];
            }
        }
    }

    build() {
        const { THREE, Sky } = this.ctx.imports;

        // 天空
        if (this.sky == null) {
            const sky = new Sky();
            sky.scale.setScalar(450000);
            this.ctx.scene.add(sky);
            this.sky = sky;
        }
        this.sky.material.uniforms.turbidity.value = this.data.turbidity;
        this.sky.material.uniforms.rayleigh.value = this.data.rayleigh;
        this.sky.material.uniforms.mieCoefficient.value = this.data.mieCoefficient;
        this.sky.material.uniforms.mieDirectionalG.value = this.data.mieDirectionalG;


        // 太阳
        if (this.sun == null) {
            const sun = new THREE.Vector3();
            // sun.setFromSphericalCoords(1, data.elevation * Math.PI / 180, data.azimuth * Math.PI / 180);
            this.sun = sun;
        }

        // 太阳位置
        const phi = THREE.MathUtils.degToRad(90 - this.data.elevation);
        const theta = THREE.MathUtils.degToRad(this.data.azimuth);
        this.sun.setFromSphericalCoords(1, phi, theta);
        this.sky.material.uniforms.sunPosition.value.copy(this.sun);

        // 添加太阳光源（方向光）
        if (this.sunLight == null) {
            // // 太阳高度
            // let sunHeight = 1000;
            // // 太阳距离场景中心的水平距离
            // let sunDistance = 2000;

            // *** 点光源 ***
            this.sunLight = new THREE.PointLight(0xffffff, 5, 0, 0.2);
            this.sunLight.castShadow = true;
            // 设置阴影精度
            this.sunLight.shadow.mapSize.width = 1024 * 2;
            this.sunLight.shadow.mapSize.height = 1024 * 2;
            // 设置阴影范围（关键）
            this.sunLight.shadow.camera.near = 0.5;
            this.sunLight.shadow.camera.far = 100000;
            this.ctx.scene.add(this.sunLight);

            if (this.ctx.enableDevViewport) {
                // 添加点光源辅助对象
                const pointLightHelper = new THREE.PointLightHelper(this.sunLight, 5);
                this.ctx.scene.add(pointLightHelper);

                const helper = new THREE.CameraHelper(this.sunLight.shadow.camera);
                this.ctx.scene.add(helper);
            }
        }
        
        // 根据太阳的elevation和azimuth动态设置光源位置
        const sunDistance = 5000;
        const elevationRad = THREE.MathUtils.degToRad(this.data.elevation);
        const azimuthRad = THREE.MathUtils.degToRad(this.data.azimuth);
        
        // 使用球坐标计算光源位置
        this.sunLight.position.x = sunDistance * Math.cos(elevationRad) * Math.sin(azimuthRad);
        this.sunLight.position.y = sunDistance * Math.sin(elevationRad);
        this.sunLight.position.z = sunDistance * Math.cos(elevationRad) * Math.cos(azimuthRad);
        
        // 确保光源不会在地面以下
        if (this.sunLight.position.y < 100) {
            this.sunLight.position.y = 100;
        }

        this.ctx.renderer.toneMappingExposure = this.data.exposure;

        this.model = this.sky;
    }
}