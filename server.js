const axios = require('axios');
const express = require('express');
const bodyParser = require('body-parser');
const multer = require('multer');

const fs = require('fs');
const path = require('path');
const readline = require('readline');

import {Buffer} from "buffer";
const crypto = require('crypto');

// 用于主项目执行
const {start} = require('./src_backend/main-start');
// 用于派生项目执行
// const {start} = require('./lib/main-start');

start({
  axios,
  express, bodyParser, multer,
  // session, cookieParser,
  fs, path, readline,
  Buffer, crypto,
}, {
  startServer: true,
})