{"id":"default","name":"默认项目"}
{"beforeMainStartItems":[{"type":"ctx-init","onlyDevViewport":true,"data":{"canExposeContext":true,"showFPS":true,"showMemory":true}},{"type":"camera-init","onlyDevViewport":true,"data":{"position":[-95.5634006849477,146.95803279796226,-991.5475999371891],"rotation":[-2.9844617531004514,0.3994879861977781,3.0800469351957758,"XYZ"],"zoom":1,"fov":50,"near":0.1,"far":100000,"aspect":1.0572972972972974,"up":[0,1,0],"target":[-95.95234738029075,146.8138692925822,-990.6376893086666]}},{"type":"ctx-init","onlyViewport":true,"data":{"showFPS":true,"showMemory":true}},{"type":"world-init","data":{"unitSize":50,"gravityUnit":5}},{"type":"player-init","data":{"startPosUnit":[0.1,0,-10.6],"startLookAtPosUnit":[0,0,1],"heightUnit":1.75,"moveSpeedUnit":5,"fastMoveSpeedUnit":15,"jumpStrengthUnit":3}}],"afterMainStartItems":[{"type":"world-grid-build","onlyDevViewport":true,"data":{"__autoSave":false,"eachGridSizeUnit":1,"totalGridCount":51,"colorCenterLine":"blue","colorGrid":"silver","pos":[0,0.5,0]}},{"type":"object-build","onlyDevViewport":true,"data":{"__autoSave":false,"helper":{"type":"center-axes-indicator"}}},{"type":"FirstPersonControlsAbility-ability-build","onlyDevViewport":true,"name":"first_person_controls","data":{"__autoSave":false,"gravity":0,"collisionEnabled":false,"upDownEnabled":true}},{"type":"SelectCGObjectAbility-ability-build","onlyDevViewport":true,"name":"select-cgo-dev","data":{"__autoSave":false,"singleSelectMode":true}},{"type":"MoveSelectedCGObjectAbility-ability-build","onlyDevViewport":true,"name":"move-selected-cgo-dev","data":{"__autoSave":false}},{"type":"FirstPersonControlsAbility-ability-build","onlyViewport":true,"name":"first_person_controls","data":{"__autoSave":false}},{"type":"sky-object-build","data":{"__autoSave":false}},{"type":"light-object-build","data":{"__autoSave":false,"ambientLight":{}}},{"type":"UserControlsAbility-ability-build","name":"user_ctrl","data":{"__autoSave":false}},{"type":"GetUserChangeAnyEventAbility-ability-build","name":"get_user_change_any_event","data":{"__autoSave":false}},{"type":"CGObjectModel-object-build","data":{"eachGridSizeUnit":1,"totalGridCount":51,"colorCenterLine":"blue","colorGrid":"silver","pos":[0,0.5,0],"id":"20250807115304803-2cc337","scale":1,"isSelected":false,"canSelect":false,"posUnit":[0,0.01,0]}},{"type":"CGObjectModel-object-build","data":{"id":"20250807115304803-c9192c","pos":[0,0,0],"scale":1,"isSelected":false,"turbidity":5,"rayleigh":1,"mieCoefficient":0.05,"mieDirectionalG":0.99999,"elevation":50,"azimuth":180,"exposure":1,"posUnit":[0,0,0]}},{"type":"CGObjectModel-object-build","data":{"ambientLight":{},"id":"20250807115304803-7821b7","pos":[0,0,0],"scale":1,"isSelected":false,"posUnit":[0,0,0]}},{"type":"CGObjectModel-object-build","data":{"canSelect":false,"tags":["地面"],"pos":[0,0,0],"sizeXUnit":51,"sizeZUnit":51,"modelCreator":{"type":"ground","aroundBorderWalls":{}},"collidableObject":{},"physBody":{"mass":0},"id":"20250807115304803-a322fb","scale":1,"isSelected":false,"posUnit":[0,0,0]}},{"type":"CGObjectModel-object-build","data":{"tags":["树","植物"],"pos":[-254.99999999999997,0,-420],"modelLoader":{"url":"./data/import-model/tree-0"},"collidableObject":{},"sizeYUnit":6,"id":"20250813172338578-3b4258","scale":2,"isSelected":true,"posUnit":[-5.1,0,-8.4],"rotationY":-75}},{"type":"CGObjectModel-object-build","data":{"tags":["树","植物"],"pos":[275,0,-425],"modelLoader":{"url":"./data/import-model/tree-0"},"collidableObject":{},"sizeYUnit":6,"id":"20250813172341503-df4190","scale":2,"isSelected":false,"posUnit":[5.5,0,-8.5],"rotationY":0,"scaleY":1,"scaleX":1,"rotationZ":0}},{"type":"CGWallModel-object-build","data":{"pos":[0,0,750],"id":"20250815164241388-f215a6","scale":1,"isSelected":false,"sizeXUnit":24,"sizeYUnit":6,"sizeZUnit":0.2,"alignmentPointType":"bottom","modelCreator":{"type":"cube"},"collidableObject":{},"posUnit":[0,0,15]}},{"type":"CGPlayerStartModel-object-build","data":{"__onlyDevViewport":true,"pos":[5,0,-530],"id":"20250815184226816-193f36","scale":1,"isSelected":false,"posUnit":[0.1,0,-10.6],"lookAtPosUnitX":0,"lookAtPosUnitY":0,"lookAtPosUnitZ":1}},{"type":"CGWallModel-object-build","data":{"pos":[-595,0,254.99999999999997],"id":"20250815223456891-4bd6de","scale":1,"isSelected":false,"sizeXUnit":20,"sizeYUnit":6,"sizeZUnit":0.2,"alignmentPointType":"bottom","modelCreator":{"type":"cube"},"collidableObject":{},"posUnit":[-11.9,0,5.1],"rotationY":-90}},{"type":"CGPaintingFrameModel-object-build","data":{"pos":[315,50,740],"id":"20250817175707391-2d0dee","scale":1,"isSelected":false,"modelLoader":{"url":"./data/import-model/frame-0"},"posUnit":[6.3,1,14.8],"rotationY":180,"imageSource":"read-book.jpg"}},{"type":"CGWallModel-object-build","data":{"pos":[595,0,254.99999999999997],"id":"20250817204153539-72944b","scale":1,"isSelected":false,"sizeXUnit":20,"sizeYUnit":6,"sizeZUnit":0.2,"alignmentPointType":"bottom","modelCreator":{"type":"cube"},"collidableObject":{},"posUnit":[11.9,0,5.1],"rotationY":-90}},{"type":"CGPaintingFrameModel-object-build","data":{"pos":[-310,50,740],"id":"20250817205131660-e4d5dc","scale":1,"isSelected":false,"modelLoader":{"url":"./data/import-model/frame-0"},"posUnit":[-6.2,1,14.8],"rotationY":-180,"imageSource":"space-ship.jpg"}},{"type":"CGWallModel-object-build","data":{"pos":[350,0,-240],"id":"20250817220428425-f70661","scale":1,"isSelected":false,"sizeXUnit":10,"sizeYUnit":6,"sizeZUnit":0.2,"alignmentPointType":"bottom","modelCreator":{"type":"cube"},"collidableObject":{},"posUnit":[7,0,-4.8]}},{"type":"CGWallModel-object-build","data":{"pos":[-350,0,-240],"id":"20250817220443493-637296","scale":1,"isSelected":false,"sizeXUnit":10,"sizeYUnit":6,"sizeZUnit":0.2,"alignmentPointType":"bottom","modelCreator":{"type":"cube"},"collidableObject":{},"posUnit":[-7,0,-4.8]}},{"type":"CGWallModel-object-build","data":{"pos":[0,150,-240],"id":"20250818100926529-edec4f","scale":1,"isSelected":false,"sizeXUnit":4,"sizeYUnit":3,"sizeZUnit":0.2,"alignmentPointType":"bottom","modelCreator":{"type":"cube"},"collidableObject":{},"posUnit":[0,3,-4.8]}},{"type":"CGObjectModel-object-build","data":{"tags":["展品","室内"],"pos":[-15,0,370],"modelLoader":{"url":"./data/import-model/exhibit-0"},"collidableObject":{},"id":"20250818122750526-34bd6d","scale":1,"isSelected":false,"posUnit":[-0.3,0,7.4],"rotationY":-180}},{"type":"CGFloorModel-object-build","data":{"pos":[0,300,254.99999999999997],"id":"20250818155400820-79a442","scale":1,"isSelected":false,"sizeXUnit":24,"sizeYUnit":0.2,"sizeZUnit":20,"modelCreator":{"type":"cube"},"collidableObject":{},"posUnit":[0,6,5.1]}},{"type":"CGBulbModel-object-build","data":{"tags":["灯","室内"],"pos":[-20,290,20],"id":"20250818160805503-8e594e","scale":1,"isSelected":false,"intensity":2,"posUnit":[-0.4,5.8,0.4]}},{"type":"CGWallMountedTelevisionModel-object-build","data":{"tags":["电视","室内"],"pos":[585,20,215],"id":"20250818200001545-6c6fdd","scale":1,"isSelected":false,"videoSource":"test-2.mp4","sizeXUnit":10,"sizeYUnit":5,"sizeZUnit":0,"posUnit":[11.7,0.4,4.3],"rotationY":-90}},{"type":"CGWallMountedTelevisionModel-object-build","data":{"tags":["电视","室内"],"pos":[-555,20,195],"id":"20250818204230499-97ce4e","scale":1,"isSelected":false,"videoSource":"test-1.mp4","sizeXUnit":10,"sizeYUnit":5,"sizeZUnit":0.1,"posUnit":[-11.1,0.4,3.9],"rotationY":90}}]}