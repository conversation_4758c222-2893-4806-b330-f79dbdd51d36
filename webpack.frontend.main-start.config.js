const path = require('path')

module.exports = {
    entry: "./src/main-start.js",
    // mode: "production",
    mode: "development",
    output: {
        // path: __dirname + '/dist',
        path: path.resolve(__dirname, 'public/src'),
        filename: "main-start.js",
        
        // globalObject: "this",
        // library: 'game-editor-frontend',
        // libraryTarget: "umd",
    },
    target: 'webworker' // 'webworker' or 'node' or 'node-webkit'
};